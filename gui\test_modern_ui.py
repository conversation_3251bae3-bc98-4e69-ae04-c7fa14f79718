#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
现代化UI测试脚本 - 参考Mihomo Party设计
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from gui.main_window import MainWindow
from gui.styles.style_manager import style_manager


def main():
    """主函数"""
    # 启用高DPI支持
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    # 创建应用程序实例
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("木偶AI翻唱 - 现代化UI")
    app.setApplicationVersion("2.0.0")
    app.setOrganizationName("AI Music Studio")
    
    # 应用现代化深色主题
    style_manager.apply_modern_dark_theme(app)
    
    # 创建并显示主窗口
    window = MainWindow()
    window.show()
    
    print("🎵 现代化UI测试启动成功")
    print("🎨 设计特色:")
    print("   ✨ 参考Mihomo Party的现代化设计")
    print("   🎯 iOS风格的圆角和开关")
    print("   🔵 蓝色强调色 (#007AFF)")
    print("   📱 现代化卡片布局")
    print("   🎛️ 智能折叠面板系统")
    print("   ⚡ 流畅的动画效果")
    print("")
    print("🔧 智能折叠功能:")
    print("   📂 左侧：成品歌曲展开时占满整个左侧区域")
    print("   ⚙️ 右侧：面板互斥展开，展开的面板占满剩余区域")
    print("   🎚️ 现代化开关替代传统复选框")
    print("   📐 紧凑的4列参数布局")
    print("")
    print("🎮 测试要点:")
    print("   - 点击左侧'我的成品歌曲'查看智能展开效果")
    print("   - 点击右侧各个面板测试互斥展开")
    print("   - 体验现代化开关的iOS风格动画")
    print("   - 调整窗口大小测试响应式效果")
    print("   - 查看新的圆角卡片设计和蓝色强调色")
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
