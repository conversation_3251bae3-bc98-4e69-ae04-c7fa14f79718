/* 现代化深色主题 - 参考Mihomo Party设计 */
/* 基于现代化UI设计原则和Mihomo Party的视觉风格 */

/* 全局应用程序样式 */
QApplication {
    font-family: "SF Pro Display", "Segoe UI", "Roboto", "Helvetica Neue", sans-serif;
    font-size: 14px;
}

/* 主窗口样式 - 深色背景 */
QMainWindow {
    background-color: #1a1a1a;
    color: #ffffff;
}

/* 通用Widget样式 */
QWidget {
    background-color: #1a1a1a;
    color: #ffffff;
    font-family: "SF Pro Display", "Segoe UI", "Roboto", "Helvetica Neue", sans-serif;
}

/* 现代化卡片样式 - 圆角设计 */
QFrame {
    background-color: #2a2a2a;
    border: 1px solid #3a3a3a;
    border-radius: 12px;
    padding: 20px;
}

/* 分割器样式 - 更细更现代 */
QSplitter::handle {
    background-color: #3a3a3a;
    width: 1px;
    height: 1px;
}

QSplitter::handle:hover {
    background-color: #4a4a4a;
}

/* 标签文本样式 */
QLabel {
    background-color: transparent;
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    padding: 4px 0px;
}

/* 标题样式 */
QLabel[class="title"] {
    font-size: 18px;
    font-weight: 600;
    color: #ffffff;
    padding: 8px 0px;
}

/* 次要文本样式 */
QLabel[class="secondary"] {
    font-size: 12px;
    color: #8a8a8a;
    font-weight: 400;
    padding: 4px 0px;
}

/* 控件标签样式 */
QLabel[class="control"] {
    font-size: 12px;
    color: #b0b0b0;
    font-weight: 500;
    padding: 2px 0px;
}

/* 现代化按钮样式 - iOS风格 */
QPushButton {
    background-color: #007AFF;
    color: #ffffff;
    border: none;
    border-radius: 8px;
    padding: 12px 20px;
    font-size: 14px;
    font-weight: 600;
    min-height: 20px;
}

QPushButton:hover {
    background-color: #0056CC;
}

QPushButton:pressed {
    background-color: #004499;
    transform: scale(0.98);
}

QPushButton:disabled {
    background-color: #3a3a3a;
    color: #6a6a6a;
}

/* 成功按钮样式 */
QPushButton[class="success"] {
    background-color: #34C759;
}

QPushButton[class="success"]:hover {
    background-color: #28A745;
}

QPushButton[class="success"]:pressed {
    background-color: #1E7E34;
}

/* 危险按钮样式 */
QPushButton[class="danger"] {
    background-color: #FF3B30;
}

QPushButton[class="danger"]:hover {
    background-color: #DC3545;
}

QPushButton[class="danger"]:pressed {
    background-color: #C82333;
}

/* 次要按钮样式 */
QPushButton[class="secondary"] {
    background-color: #3a3a3a;
    color: #ffffff;
}

QPushButton[class="secondary"]:hover {
    background-color: #4a4a4a;
}

QPushButton[class="secondary"]:pressed {
    background-color: #2a2a2a;
}

/* 现代化输入框样式 */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #2a2a2a;
    color: #ffffff;
    border: 1px solid #3a3a3a;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    min-height: 20px;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #007AFF;
    outline: none;
    background-color: #2d2d2d;
}

/* 现代化下拉框样式 */
QComboBox {
    background-color: #2a2a2a;
    color: #ffffff;
    border: 1px solid #3a3a3a;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 14px;
    min-height: 20px;
}

QComboBox:hover {
    border-color: #4a4a4a;
    background-color: #2d2d2d;
}

QComboBox:focus {
    border-color: #007AFF;
}

QComboBox::drop-down {
    border: none;
    width: 24px;
    padding-right: 8px;
}

QComboBox::down-arrow {
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iI2ZmZmZmZiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
    width: 12px;
    height: 8px;
}

QComboBox QAbstractItemView {
    background-color: #2a2a2a;
    color: #ffffff;
    border: 1px solid #3a3a3a;
    border-radius: 8px;
    selection-background-color: #007AFF;
    outline: none;
}

/* 现代化滑块样式 */
QSlider::groove:horizontal {
    background-color: #3a3a3a;
    height: 6px;
    border-radius: 3px;
}

QSlider::handle:horizontal {
    background-color: #007AFF;
    width: 20px;
    height: 20px;
    border-radius: 10px;
    margin: -7px 0;
    border: 2px solid #ffffff;
}

QSlider::handle:horizontal:hover {
    background-color: #0056CC;
    transform: scale(1.1);
}

QSlider::handle:horizontal:pressed {
    background-color: #004499;
}

/* iOS风格开关样式 */
QCheckBox {
    color: #ffffff;
    font-size: 14px;
    font-weight: 500;
    spacing: 12px;
}

QCheckBox::indicator {
    width: 20px;
    height: 20px;
    border: 2px solid #3a3a3a;
    border-radius: 6px;
    background-color: #2a2a2a;
}

QCheckBox::indicator:hover {
    border-color: #4a4a4a;
    background-color: #2d2d2d;
}

QCheckBox::indicator:checked {
    background-color: #007AFF;
    border-color: #007AFF;
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOSIgdmlld0JveD0iMCAwIDEyIDkiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDQuNUw0LjUgOEwxMSAxIiBzdHJva2U9IndoaXRlIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K);
}

/* 现代化滚动条样式 */
QScrollBar:vertical {
    background-color: transparent;
    width: 8px;
    border-radius: 4px;
    margin: 0px;
}

QScrollBar::handle:vertical {
    background-color: #4a4a4a;
    border-radius: 4px;
    min-height: 20px;
    margin: 0px;
}

QScrollBar::handle:vertical:hover {
    background-color: #5a5a5a;
}

QScrollBar::handle:vertical:pressed {
    background-color: #007AFF;
}

QScrollBar::add-line:vertical,
QScrollBar::sub-line:vertical {
    height: 0px;
    width: 0px;
}

QScrollBar::add-page:vertical,
QScrollBar::sub-page:vertical {
    background: transparent;
}

QScrollBar:horizontal {
    background-color: transparent;
    height: 8px;
    border-radius: 4px;
    margin: 0px;
}

QScrollBar::handle:horizontal {
    background-color: #4a4a4a;
    border-radius: 4px;
    min-width: 20px;
    margin: 0px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #5a5a5a;
}

QScrollBar::handle:horizontal:pressed {
    background-color: #007AFF;
}

QScrollBar::add-line:horizontal,
QScrollBar::sub-line:horizontal {
    height: 0px;
    width: 0px;
}

QScrollBar::add-page:horizontal,
QScrollBar::sub-page:horizontal {
    background: transparent;
}

/* 现代化标签页样式 */
QTabWidget::pane {
    border: none;
    background-color: transparent;
    border-radius: 12px;
}

QTabBar::tab {
    background-color: #2a2a2a;
    color: #8a8a8a;
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    margin-right: 4px;
    margin-bottom: 4px;
    font-weight: 500;
}

QTabBar::tab:hover {
    background-color: #3a3a3a;
    color: #b0b0b0;
}

QTabBar::tab:selected {
    background-color: #007AFF;
    color: #ffffff;
    font-weight: 600;
}

/* 工具提示样式 */
QToolTip {
    background-color: #2a2a2a;
    color: #ffffff;
    border: 1px solid #3a3a3a;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 12px;
}

/* 列表控件样式 */
QListWidget {
    background-color: transparent;
    border: none;
    outline: none;
    border-radius: 8px;
}

QListWidget::item {
    padding: 12px 16px;
    border-bottom: 1px solid #3a3a3a;
    color: #ffffff;
    border-radius: 8px;
    margin-bottom: 2px;
}

QListWidget::item:hover {
    background-color: #3a3a3a;
}

QListWidget::item:selected {
    background-color: #007AFF;
    color: #ffffff;
}

/* 滚动区域样式 */
QScrollArea {
    background-color: transparent;
    border: none;
    border-radius: 12px;
}
