#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件上传组件 - 支持拖拽和点击上传
"""

import os
from PySide6.QtWidgets import (
    QWidget, QVBoxLayout, QLabel, QFileDialog, QFrame
)
from PySide6.QtCore import Qt, Signal, QMimeData
from PySide6.QtGui import QDragEnterEvent, QDropEvent, QMouseEvent, QPainter, QPen


class FileUploadWidget(QFrame):
    """文件上传组件 - 支持拖拽和点击上传"""
    
    # 信号定义
    file_selected = Signal(str)  # 文件选择信号，传递文件路径
    file_dropped = Signal(str)   # 文件拖拽信号，传递文件路径
    
    def __init__(self, parent=None):
        super().__init__(parent)

        # 支持的音频文件格式
        self.supported_formats = [
            '.wav', '.mp3', '.flac', '.aac', '.ogg', '.m4a', '.wma'
        ]

        # 状态变量
        self.is_drag_over = False
        self.selected_file = None

        self.init_ui()
        self.setup_drag_drop()
        
    def init_ui(self):
        """初始化UI"""
        self.setFrameStyle(QFrame.StyledPanel)
        self.setMinimumHeight(150)
        self.setMaximumHeight(200)
        
        # 创建布局
        layout = QVBoxLayout(self)
        layout.setContentsMargins(20, 20, 20, 20)
        layout.setSpacing(10)
        
        # 创建图标标签（使用文本代替图标）
        self.icon_label = QLabel("📁")
        self.icon_label.setAlignment(Qt.AlignCenter)
        self.icon_label.setStyleSheet("font-size: 48px;")
        layout.addWidget(self.icon_label)
        
        # 创建提示文本
        self.text_label = QLabel("在此处选择或拖拽音频文件")
        self.text_label.setAlignment(Qt.AlignCenter)
        self.text_label.setProperty("class", "secondary")
        self.text_label.setWordWrap(True)
        layout.addWidget(self.text_label)
        
        # 设置样式
        self.update_style()
        
    def setup_drag_drop(self):
        """设置拖拽功能"""
        self.setAcceptDrops(True)
        
    def update_style(self):
        """更新样式"""
        if self.is_drag_over:
            # 拖拽悬停状态
            self.setStyleSheet("""
                QFrame {
                    border: 2px dashed #3B82F6;
                    border-radius: 8px;
                    background-color: rgba(59, 130, 246, 0.1);
                }
            """)
        elif self.selected_file:
            # 文件已选择状态
            self.setStyleSheet("""
                QFrame {
                    border: 2px solid #4CAF50;
                    border-radius: 8px;
                    background-color: rgba(76, 175, 80, 0.1);
                }
            """)
        else:
            # 默认状态
            self.setStyleSheet("""
                QFrame {
                    border: 2px dashed #1E88E5;
                    border-radius: 8px;
                    background-color: transparent;
                }
                QFrame:hover {
                    background-color: rgba(30, 136, 229, 0.05);
                }
            """)
    
    def dragEnterEvent(self, event: QDragEnterEvent):
        """拖拽进入事件"""
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if urls and self.is_audio_file(urls[0].toLocalFile()):
                event.acceptProposedAction()
                self.is_drag_over = True
                self.update_style()
                return
        event.ignore()
    
    def dragLeaveEvent(self, event):
        """拖拽离开事件"""
        self.is_drag_over = False
        self.update_style()
        super().dragLeaveEvent(event)
    
    def dropEvent(self, event: QDropEvent):
        """拖拽放下事件"""
        self.is_drag_over = False
        
        if event.mimeData().hasUrls():
            urls = event.mimeData().urls()
            if urls:
                file_path = urls[0].toLocalFile()
                if self.is_audio_file(file_path):
                    self.handle_file_selection(file_path)
                    self.file_dropped.emit(file_path)
                    event.acceptProposedAction()
                    return
        
        self.update_style()
        event.ignore()
    
    def mousePressEvent(self, event: QMouseEvent):
        """鼠标点击事件 - 打开文件对话框"""
        if event.button() == Qt.LeftButton:
            self.open_file_dialog()
        super().mousePressEvent(event)
    
    def open_file_dialog(self):
        """打开文件选择对话框"""
        file_filter = "音频文件 (*.wav *.mp3 *.flac *.aac *.ogg *.m4a *.wma);;所有文件 (*.*)"
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择音频文件",
            "",
            file_filter
        )
        
        if file_path and self.is_audio_file(file_path):
            self.handle_file_selection(file_path)
            self.file_selected.emit(file_path)
    
    def handle_file_selection(self, file_path):
        """处理文件选择"""
        self.selected_file = file_path
        file_name = os.path.basename(file_path)
        
        # 更新显示
        self.icon_label.setText("🎵")
        self.text_label.setText(f"已选择: {file_name}")
        self.update_style()
        
        print(f"✓ 文件已选择: {file_name}")
    
    def is_audio_file(self, file_path):
        """检查是否为支持的音频文件"""
        if not file_path:
            return False
            
        file_ext = os.path.splitext(file_path)[1].lower()
        return file_ext in self.supported_formats
    
    def clear_selection(self):
        """清除选择"""
        self.selected_file = None
        self.icon_label.setText("📁")
        self.text_label.setText("在此处选择或拖拽音频文件")
        self.update_style()
    
    def get_selected_file(self):
        """获取选择的文件路径"""
        return self.selected_file
