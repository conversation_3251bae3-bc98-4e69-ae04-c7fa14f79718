#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据模型定义 - 配置、歌曲、参数数据结构
"""

import os
from datetime import datetime
from enum import Enum
from dataclasses import dataclass, field
from typing import Optional, List, Dict, Any


class ProcessingStatus(Enum):
    """处理状态枚举"""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"


class ProcessingMode(Enum):
    """处理模式枚举"""
    COMPLETE = "完整模式"
    DRY_VOICE = "干声模式"


class OutputFormat(Enum):
    """输出格式枚举"""
    WAV = "WAV"
    MP3 = "MP3"


@dataclass
class ConfigData:
    """配置数据模型"""
    # API配置
    api_url: str = "http://127.0.0.1:9880"
    python_env_path: str = "workenv\\python.exe"
    auto_start_api: bool = True
    cloud_api: bool = True
    
    # 处理配置
    processing_mode: ProcessingMode = ProcessingMode.COMPLETE
    output_format: OutputFormat = OutputFormat.WAV
    
    # 窗口配置
    window_width: int = 1200
    window_height: int = 800
    window_x: int = 100
    window_y: int = 100
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'api_url': self.api_url,
            'python_env_path': self.python_env_path,
            'auto_start_api': self.auto_start_api,
            'cloud_api': self.cloud_api,
            'processing_mode': self.processing_mode.value,
            'output_format': self.output_format.value,
            'window_width': self.window_width,
            'window_height': self.window_height,
            'window_x': self.window_x,
            'window_y': self.window_y
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ConfigData':
        """从字典创建"""
        config = cls()
        config.api_url = data.get('api_url', config.api_url)
        config.python_env_path = data.get('python_env_path', config.python_env_path)
        config.auto_start_api = data.get('auto_start_api', config.auto_start_api)
        config.cloud_api = data.get('cloud_api', config.cloud_api)
        
        # 处理枚举类型
        mode_value = data.get('processing_mode', config.processing_mode.value)
        config.processing_mode = ProcessingMode(mode_value)
        
        format_value = data.get('output_format', config.output_format.value)
        config.output_format = OutputFormat(format_value)
        
        config.window_width = data.get('window_width', config.window_width)
        config.window_height = data.get('window_height', config.window_height)
        config.window_x = data.get('window_x', config.window_x)
        config.window_y = data.get('window_y', config.window_y)
        
        return config


@dataclass
class SongData:
    """歌曲数据模型"""
    # 基本信息
    file_path: str
    file_name: str = ""
    file_size: int = 0
    duration: float = 0.0  # 秒
    
    # 处理状态
    status: ProcessingStatus = ProcessingStatus.PENDING
    created_time: datetime = field(default_factory=datetime.now)
    processed_time: Optional[datetime] = None
    
    # 输出文件
    output_files: Dict[str, str] = field(default_factory=dict)  # 类型 -> 文件路径
    
    # 处理参数（快照）
    processing_params: Optional['ParameterData'] = None
    
    # 错误信息
    error_message: str = ""
    
    def __post_init__(self):
        """初始化后处理"""
        if not self.file_name:
            self.file_name = os.path.basename(self.file_path)
        
        if self.file_size == 0 and os.path.exists(self.file_path):
            self.file_size = os.path.getsize(self.file_path)
    
    def get_display_name(self) -> str:
        """获取显示名称"""
        name, ext = os.path.splitext(self.file_name)
        return name
    
    def get_status_text(self) -> str:
        """获取状态文本"""
        status_map = {
            ProcessingStatus.PENDING: "等待处理",
            ProcessingStatus.PROCESSING: "处理中",
            ProcessingStatus.COMPLETED: "已完成",
            ProcessingStatus.FAILED: "处理失败"
        }
        return status_map.get(self.status, "未知状态")
    
    def get_file_size_text(self) -> str:
        """获取文件大小文本"""
        if self.file_size < 1024:
            return f"{self.file_size} B"
        elif self.file_size < 1024 * 1024:
            return f"{self.file_size / 1024:.1f} KB"
        else:
            return f"{self.file_size / (1024 * 1024):.1f} MB"
    
    def get_duration_text(self) -> str:
        """获取时长文本"""
        if self.duration <= 0:
            return "未知"
        
        minutes = int(self.duration // 60)
        seconds = int(self.duration % 60)
        return f"{minutes}:{seconds:02d}"


@dataclass
class ParameterData:
    """参数数据模型"""
    # 模型配置
    voice_model: str = "YSML.pt"
    config_file: str = "YSML.yaml"
    human_pitch: int = 0
    instrumental_pitch: int = 0
    
    # 混响与和声
    enable_reverb: bool = True
    harmony_to_accompaniment: bool = False
    room_size: float = 0.5
    reverb_damping: float = 0.5
    reverb_width: float = 1.0
    wet_level: float = 0.33
    dry_level: float = 0.4
    delay_seconds: float = 0.0
    
    # 高级音频参数
    enable_advanced: bool = False
    comp_threshold: int = -20
    comp_ratio: float = 4.0
    chorus_rate: float = 1.0
    chorus_depth: float = 0.25
    dist_drive: int = 25
    
    # 推理参数
    f0_extractor: str = "rmvpe (默认)"
    pitch_shift: int = 0
    sampling_steps: int = 50
    sampler: str = "euler"
    device: str = "CUDA (默认)"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'voice_model': self.voice_model,
            'config_file': self.config_file,
            'human_pitch': self.human_pitch,
            'instrumental_pitch': self.instrumental_pitch,
            'enable_reverb': self.enable_reverb,
            'harmony_to_accompaniment': self.harmony_to_accompaniment,
            'room_size': self.room_size,
            'reverb_damping': self.reverb_damping,
            'reverb_width': self.reverb_width,
            'wet_level': self.wet_level,
            'dry_level': self.dry_level,
            'delay_seconds': self.delay_seconds,
            'enable_advanced': self.enable_advanced,
            'comp_threshold': self.comp_threshold,
            'comp_ratio': self.comp_ratio,
            'chorus_rate': self.chorus_rate,
            'chorus_depth': self.chorus_depth,
            'dist_drive': self.dist_drive,
            'f0_extractor': self.f0_extractor,
            'pitch_shift': self.pitch_shift,
            'sampling_steps': self.sampling_steps,
            'sampler': self.sampler,
            'device': self.device
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'ParameterData':
        """从字典创建"""
        return cls(**{k: v for k, v in data.items() if hasattr(cls, k)})


# 全局数据实例
app_config = ConfigData()
current_parameters = ParameterData()
song_list: List[SongData] = []
