#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI翻唱桌面应用程序 - 主窗口
基于PySide6实现，完全复刻HTML界面设计
"""

import sys
import os
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QSplitter, QLabel, QFrame, QTabWidget, QComboBox, QPushButton,
    QGridLayout, QListWidget, QListWidgetItem, QMenu, QMessageBox,
    QLineEdit, QTextEdit, QFileDialog, QCheckBox, QSizePolicy
)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QFont, QPalette, QColor

# 导入样式管理器和组件
from gui.styles.style_manager import style_manager
from gui.components.file_upload_widget import FileUploadWidget
from gui.components.audio_player_widget import AudioPlayerWidget
from gui.components.collapsible_panel_widget import CollapsiblePanelWidget
from gui.components.custom_slider_widget import (
    PitchSliderWidget, ReverbSliderWidget, DelaySliderWidget,
    CompressorSliderWidget, CustomSliderWidget
)
from gui.models.data_models import SongData, ProcessingStatus


class MainWindow(QMainWindow):
    """主窗口类 - 实现基本的左右分栏布局"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_layout()
        
    def init_ui(self):
        """初始化UI基本设置"""
        # 设置窗口标题和图标
        self.setWindowTitle("AI翻唱")
        
        # 设置窗口大小和最小尺寸
        self.setMinimumSize(QSize(900, 600))
        self.resize(1200, 800)
        
        # 设置窗口居中显示
        self.center_window()
        
    def center_window(self):
        """将窗口居中显示"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
        
    def setup_layout(self):
        """设置主布局 - 左右分栏 1:3 比例"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建水平分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 创建左侧面板 (1/3)
        self.left_panel = self.create_left_panel()
        splitter.addWidget(self.left_panel)
        
        # 创建右侧面板 (2/3)
        self.right_panel = self.create_right_panel()
        splitter.addWidget(self.right_panel)
        
        # 设置分割器比例 1:3
        splitter.setSizes([300, 900])  # 左侧300px，右侧900px
        splitter.setStretchFactor(0, 1)  # 左侧拉伸因子
        splitter.setStretchFactor(1, 3)  # 右侧拉伸因子

        # 设置分割器样式和约束
        splitter.setHandleWidth(2)
        splitter.setChildrenCollapsible(False)  # 防止面板被完全折叠

        # 设置左右面板的大小策略
        self.left_panel.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Expanding)
        self.right_panel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
    def create_left_panel(self):
        """创建左侧面板"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)

        # 创建文件上传区域
        self.file_upload_widget = FileUploadWidget()
        self.file_upload_widget.file_selected.connect(self.on_file_selected)
        self.file_upload_widget.file_dropped.connect(self.on_file_dropped)
        layout.addWidget(self.file_upload_widget)

        # 创建音频播放器
        self.audio_player_widget = AudioPlayerWidget()
        self.audio_player_widget.playback_started.connect(self.on_playback_started)
        self.audio_player_widget.playback_paused.connect(self.on_playback_paused)
        self.audio_player_widget.playback_stopped.connect(self.on_playback_stopped)
        layout.addWidget(self.audio_player_widget)

        # 创建处理选项区域
        self.create_processing_options(layout)

        # 创建一键翻唱按钮
        self.create_main_button(layout)

        # 创建歌曲列表
        self.create_song_list(layout)

        # 添加弹性空间
        layout.addStretch()

        return panel

    def create_song_list(self, layout):
        """创建歌曲列表"""
        # 创建可折叠的歌曲列表面板
        self.song_list_panel = CollapsiblePanelWidget("我的成品歌曲", expanded=False)

        # 创建歌曲列表控件
        self.song_list_widget = QListWidget()
        self.song_list_widget.setMaximumHeight(220)
        self.song_list_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.song_list_widget.customContextMenuRequested.connect(self.show_song_context_menu)
        self.song_list_widget.setStyleSheet("""
            QListWidget {
                background-color: transparent;
                border: none;
                outline: none;
            }
            QListWidget::item {
                padding: 8px;
                border-bottom: 1px solid #394B61;
                color: #E5E7EB;
            }
            QListWidget::item:hover {
                background-color: #394B61;
            }
            QListWidget::item:selected {
                background-color: #1E88E5;
            }
        """)

        # 添加到面板
        self.song_list_panel.add_content_widget(self.song_list_widget)
        layout.addWidget(self.song_list_panel)

        # 初始化歌曲数据
        self.song_data_list = []
        self.load_sample_songs()

    def load_sample_songs(self):
        """加载示例歌曲数据"""
        sample_songs = [
            "晴天 - 周杰伦.wav",
            "富士山下 - 陈奕迅.wav",
            "像我这样的人 - 毛不易.wav",
            "爱你 - 王心凌.wav",
            "稻香 - 周杰伦.wav",
            "起风了 - 吴青峰.wav",
            "孤勇者 - 陈奕迅.wav"
        ]

        for song_name in sample_songs:
            # 创建歌曲数据
            song_data = SongData(
                file_path=f"results/{song_name}",
                duration=180.0 + len(song_name) * 2  # 模拟不同时长
            )
            song_data.status = ProcessingStatus.COMPLETED

            self.song_data_list.append(song_data)
            self.add_song_to_list(song_data)

    def add_song_to_list(self, song_data: SongData):
        """添加歌曲到列表"""
        # 创建列表项
        item = QListWidgetItem()

        # 设置显示文本
        display_text = f"{song_data.get_display_name()}\n{song_data.get_status_text()} | {song_data.get_duration_text()}"
        item.setText(display_text)

        # 存储歌曲数据
        item.setData(Qt.UserRole, song_data)

        # 添加到列表
        self.song_list_widget.addItem(item)

    def add_new_song(self, file_path: str):
        """添加新歌曲"""
        song_data = SongData(file_path=file_path)
        self.song_data_list.append(song_data)
        self.add_song_to_list(song_data)

        # 展开歌曲列表面板
        if not self.song_list_panel.is_expanded:
            self.song_list_panel.expand()

    def show_song_context_menu(self, position):
        """显示歌曲右键菜单"""
        item = self.song_list_widget.itemAt(position)
        if item is None:
            return

        # 获取歌曲数据
        song_data = item.data(Qt.UserRole)
        if song_data is None:
            return

        # 创建上下文菜单
        menu = QMenu(self)
        menu.setStyleSheet("""
            QMenu {
                background-color: #293746;
                border: 1px solid #455A79;
                border-radius: 4px;
                color: #E0E0E0;
                padding: 4px;
            }
            QMenu::item {
                padding: 8px 12px;
                border-radius: 2px;
            }
            QMenu::item:selected {
                background-color: #1E88E5;
            }
            QMenu::separator {
                height: 1px;
                background-color: #455A79;
                margin: 4px 8px;
            }
        """)

        # 添加菜单项
        rename_action = menu.addAction("🏷️ 重命名")
        remix_action = menu.addAction("🎵 重新混音")
        delete_action = menu.addAction("🗑️ 删除")
        menu.addSeparator()
        open_folder_action = menu.addAction("📁 打开文件夹")

        # 连接信号
        rename_action.triggered.connect(lambda: self.rename_song(song_data))
        remix_action.triggered.connect(lambda: self.remix_song(song_data))
        delete_action.triggered.connect(lambda: self.delete_song(song_data))
        open_folder_action.triggered.connect(lambda: self.open_song_folder(song_data))

        # 显示菜单
        global_pos = self.song_list_widget.mapToGlobal(position)
        menu.exec(global_pos)

    def rename_song(self, song_data: SongData):
        """重命名歌曲"""
        print(f"重命名歌曲: {song_data.get_display_name()}")
        # TODO: 实现重命名对话框
        QMessageBox.information(self, "功能提示", f"重命名功能：{song_data.get_display_name()}")

    def remix_song(self, song_data: SongData):
        """重新混音"""
        print(f"重新混音: {song_data.get_display_name()}")
        # TODO: 实现重新混音功能
        QMessageBox.information(self, "功能提示", f"重新混音功能：{song_data.get_display_name()}")

    def delete_song(self, song_data: SongData):
        """删除歌曲"""
        print(f"删除歌曲: {song_data.get_display_name()}")

        # 确认对话框
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除歌曲 '{song_data.get_display_name()}' 吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 从列表中移除
            for i in range(self.song_list_widget.count()):
                item = self.song_list_widget.item(i)
                if item.data(Qt.UserRole) == song_data:
                    self.song_list_widget.takeItem(i)
                    break

            # 从数据列表中移除
            if song_data in self.song_data_list:
                self.song_data_list.remove(song_data)

            print(f"✓ 歌曲已删除: {song_data.get_display_name()}")

    def open_song_folder(self, song_data: SongData):
        """打开歌曲文件夹"""
        print(f"打开文件夹: {song_data.file_path}")

        import subprocess
        import os

        try:
            # 获取文件所在目录
            folder_path = os.path.dirname(song_data.file_path)

            # 在Windows上打开文件夹
            if os.name == 'nt':
                subprocess.run(['explorer', folder_path])
            else:
                # Linux/Mac
                subprocess.run(['xdg-open', folder_path])

            print(f"✓ 已打开文件夹: {folder_path}")
        except Exception as e:
            print(f"✗ 打开文件夹失败: {e}")
            QMessageBox.warning(self, "错误", f"无法打开文件夹：{e}")

    def browse_python_path(self):
        """浏览Python路径"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择Python解释器",
            "",
            "可执行文件 (*.exe);;所有文件 (*.*)"
        )

        if file_path:
            self.python_path_input.setText(file_path)
            print(f"✓ Python路径已设置: {file_path}")

    def start_api(self):
        """启动API"""
        api_url = self.api_url_input.text()
        python_path = self.python_path_input.text()

        print(f"启动API: {api_url}")
        print(f"Python路径: {python_path}")

        # 更新控制台输出
        self.console_output.append(f"正在启动API服务器...")
        self.console_output.append(f"API URL: {api_url}")
        self.console_output.append(f"Python环境: {python_path}")
        self.console_output.append(f"自动启动: {'是' if self.auto_start_checkbox.isChecked() else '否'}")
        self.console_output.append(f"云端API: {'是' if self.cloud_api_checkbox.isChecked() else '否'}")
        self.console_output.append("API服务器启动成功！")

        # 更新按钮状态
        self.start_api_btn.setEnabled(False)
        self.stop_api_btn.setEnabled(True)
        self.api_status_label.setText("状态: 运行中")

        print("✓ API已启动")

    def stop_api(self):
        """停止API"""
        print("停止API")

        # 更新控制台输出
        self.console_output.append("正在停止API服务器...")
        self.console_output.append("API服务器已停止。")

        # 更新按钮状态
        self.start_api_btn.setEnabled(True)
        self.stop_api_btn.setEnabled(False)
        self.api_status_label.setText("状态: 未启动")

        print("✓ API已停止")

    def create_processing_options(self, layout):
        """创建处理选项区域"""
        # 创建选项容器
        options_frame = QFrame()
        options_layout = QGridLayout(options_frame)
        options_layout.setContentsMargins(8, 8, 8, 8)
        options_layout.setSpacing(12)

        # 处理模式选择
        mode_label = QLabel("处理模式:")
        mode_label.setProperty("class", "secondary")
        options_layout.addWidget(mode_label, 0, 0)

        self.processing_mode_combo = QComboBox()
        self.processing_mode_combo.addItems(["完整模式", "干声模式"])
        self.processing_mode_combo.setCurrentIndex(0)
        self.processing_mode_combo.currentTextChanged.connect(self.on_processing_mode_changed)
        options_layout.addWidget(self.processing_mode_combo, 0, 1)

        # 输出格式选择
        format_label = QLabel("输出格式:")
        format_label.setProperty("class", "secondary")
        options_layout.addWidget(format_label, 1, 0)

        self.output_format_combo = QComboBox()
        self.output_format_combo.addItems(["WAV", "MP3"])
        self.output_format_combo.setCurrentIndex(0)
        self.output_format_combo.currentTextChanged.connect(self.on_output_format_changed)
        options_layout.addWidget(self.output_format_combo, 1, 1)

        layout.addWidget(options_frame)

    def create_main_button(self, layout):
        """创建主要操作按钮"""
        # 一键翻唱按钮
        self.main_button = QPushButton("🚀 一键翻唱")
        self.main_button.setProperty("class", "success")
        self.main_button.setMinimumHeight(50)
        self.main_button.clicked.connect(self.on_main_button_clicked)
        layout.addWidget(self.main_button)

        # 状态标签
        self.status_label = QLabel("状态: 空闲")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setProperty("class", "secondary")
        layout.addWidget(self.status_label)

    def on_file_selected(self, file_path):
        """文件选择回调"""
        print(f"文件已通过点击选择: {file_path}")
        self.load_audio_file(file_path)

    def on_file_dropped(self, file_path):
        """文件拖拽回调"""
        print(f"文件已通过拖拽选择: {file_path}")
        self.load_audio_file(file_path)

    def load_audio_file(self, file_path):
        """加载音频文件到播放器"""
        if self.audio_player_widget.load_file(file_path):
            print(f"✓ 音频文件已加载到播放器: {file_path}")
            # 添加到歌曲列表（如果不存在）
            if not any(song.file_path == file_path for song in self.song_data_list):
                self.add_new_song(file_path)
        else:
            print(f"✗ 音频文件加载失败: {file_path}")

    def on_playback_started(self):
        """播放开始回调"""
        print("▶ 音频播放开始")

    def on_playback_paused(self):
        """播放暂停回调"""
        print("⏸ 音频播放暂停")

    def on_playback_stopped(self):
        """播放停止回调"""
        print("⏹ 音频播放停止")

    def on_processing_mode_changed(self, mode):
        """处理模式变化回调"""
        print(f"处理模式已切换为: {mode}")

    def on_output_format_changed(self, format):
        """输出格式变化回调"""
        print(f"输出格式已切换为: {format}")

    def on_main_button_clicked(self):
        """一键翻唱按钮点击回调"""
        print("🚀 一键翻唱按钮被点击")
        if hasattr(self, 'audio_player_widget') and self.audio_player_widget.current_file:
            mode = self.processing_mode_combo.currentText()
            format = self.output_format_combo.currentText()
            print(f"  - 处理模式: {mode}")
            print(f"  - 输出格式: {format}")
            print(f"  - 音频文件: {self.audio_player_widget.current_file}")
            self.status_label.setText("状态: 处理中...")
        else:
            print("  - 请先选择音频文件")
            self.status_label.setText("状态: 请先选择音频文件")
        
    def create_right_panel(self):
        """创建右侧面板 - 包含标签页导航"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(0)

        # 创建标签页控件
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # 创建歌曲制作标签页
        self.song_production_tab = self.create_song_production_tab()
        self.tab_widget.addTab(self.song_production_tab, "歌曲制作")

        # 创建API管理标签页
        self.api_management_tab = self.create_api_management_tab()
        self.tab_widget.addTab(self.api_management_tab, "API管理")

        return panel

    def create_song_production_tab(self):
        """创建歌曲制作标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)

        # 创建模型与音高配置区域
        self.create_model_pitch_section(layout)

        # 创建参数配置面板
        self.create_parameter_panels(layout)

        # 添加弹性空间
        layout.addStretch()

        return tab

    def create_model_pitch_section(self, layout):
        """创建模型与音高配置区域"""
        # 创建固定的模型配置区域（不可折叠）
        model_frame = QFrame()
        model_layout = QVBoxLayout(model_frame)
        model_layout.setContentsMargins(16, 16, 16, 16)
        model_layout.setSpacing(12)

        # 标题
        title_label = QLabel("模型与音高")
        title_label.setProperty("class", "title")
        model_layout.addWidget(title_label)

        # 模型选择网格
        model_grid = QGridLayout()
        model_grid.setSpacing(12)

        # 音色模型
        model_grid.addWidget(QLabel("音色模型:"), 0, 0)
        self.voice_model_combo = QComboBox()
        self.voice_model_combo.addItems(["YSML.pt", "于洋.pt", "山海.pt"])
        model_grid.addWidget(self.voice_model_combo, 0, 1)

        # 配置文件
        model_grid.addWidget(QLabel("配置文件:"), 1, 0)
        self.config_file_combo = QComboBox()
        self.config_file_combo.addItems(["YSML.yaml", "于洋.yaml", "山海.yaml"])
        model_grid.addWidget(self.config_file_combo, 1, 1)

        # 人声音高
        model_grid.addWidget(QLabel("人声音高:"), 2, 0)
        self.human_pitch_slider = PitchSliderWidget()
        model_grid.addWidget(self.human_pitch_slider, 2, 1)

        # 伴奏音高
        model_grid.addWidget(QLabel("伴奏音高:"), 3, 0)
        self.instrumental_pitch_slider = PitchSliderWidget()
        model_grid.addWidget(self.instrumental_pitch_slider, 3, 1)

        model_layout.addLayout(model_grid)
        layout.addWidget(model_frame)

    def create_parameter_panels(self, layout):
        """创建参数配置面板"""
        # 混响与和声面板
        reverb_panel = CollapsiblePanelWidget("混响与和声", expanded=False)

        # 重新创建带复选框的标题栏
        reverb_panel.header_widget.setParent(None)
        reverb_panel.header_widget = reverb_panel.create_header_with_checkboxes(
            "混响与和声",
            [("启用混响", True), ("和声加入伴奏", False)]
        )
        reverb_panel.layout().insertWidget(0, reverb_panel.header_widget)

        # 混响参数
        reverb_grid = QGridLayout()
        reverb_grid.setSpacing(12)

        reverb_grid.addWidget(QLabel("房间大小:"), 0, 0)
        self.room_size_slider = ReverbSliderWidget(0.0, 1.0, 0.5)
        reverb_grid.addWidget(self.room_size_slider, 0, 1)

        reverb_grid.addWidget(QLabel("阻尼:"), 1, 0)
        self.reverb_damping_slider = ReverbSliderWidget(0.0, 1.0, 0.5)
        reverb_grid.addWidget(self.reverb_damping_slider, 1, 1)

        reverb_grid.addWidget(QLabel("宽度:"), 2, 0)
        self.reverb_width_slider = ReverbSliderWidget(0.0, 1.0, 1.0)
        reverb_grid.addWidget(self.reverb_width_slider, 2, 1)

        reverb_grid.addWidget(QLabel("湿润度:"), 3, 0)
        self.wet_level_slider = ReverbSliderWidget(0.0, 1.0, 0.33)
        reverb_grid.addWidget(self.wet_level_slider, 3, 1)

        reverb_grid.addWidget(QLabel("干燥度:"), 4, 0)
        self.dry_level_slider = ReverbSliderWidget(0.0, 1.0, 0.4)
        reverb_grid.addWidget(self.dry_level_slider, 4, 1)

        reverb_grid.addWidget(QLabel("延迟:"), 5, 0)
        self.delay_slider = DelaySliderWidget()
        reverb_grid.addWidget(self.delay_slider, 5, 1)

        reverb_panel.add_content_layout(reverb_grid)
        layout.addWidget(reverb_panel)

        # 高级音频参数面板
        advanced_panel = CollapsiblePanelWidget("高级音频参数", expanded=False)

        # 重新创建带复选框的标题栏
        advanced_panel.header_widget.setParent(None)
        advanced_panel.header_widget = advanced_panel.create_header_with_checkboxes(
            "高级音频参数",
            [("启用", False)]
        )
        advanced_panel.layout().insertWidget(0, advanced_panel.header_widget)

        # 高级参数内容
        advanced_content = QVBoxLayout()

        # 压缩器
        comp_label = QLabel("压缩器 (Compressor)")
        comp_label.setProperty("class", "secondary")
        advanced_content.addWidget(comp_label)

        comp_grid = QGridLayout()
        comp_grid.addWidget(QLabel("阈值 (dB):"), 0, 0)
        self.comp_threshold_slider = CompressorSliderWidget()
        comp_grid.addWidget(self.comp_threshold_slider, 0, 1)

        comp_grid.addWidget(QLabel("比率:"), 1, 0)
        self.comp_ratio_slider = CustomSliderWidget(1.0, 20.0, 4.0, decimals=1)
        comp_grid.addWidget(self.comp_ratio_slider, 1, 1)

        advanced_content.addLayout(comp_grid)

        # 合唱
        chorus_label = QLabel("合唱 (Chorus)")
        chorus_label.setProperty("class", "secondary")
        advanced_content.addWidget(chorus_label)

        chorus_grid = QGridLayout()
        chorus_grid.addWidget(QLabel("速率 (Hz):"), 0, 0)
        self.chorus_rate_slider = CustomSliderWidget(0.1, 10.0, 1.0, decimals=1, suffix="Hz")
        chorus_grid.addWidget(self.chorus_rate_slider, 0, 1)

        chorus_grid.addWidget(QLabel("深度:"), 1, 0)
        self.chorus_depth_slider = ReverbSliderWidget(0.0, 1.0, 0.25)
        chorus_grid.addWidget(self.chorus_depth_slider, 1, 1)

        advanced_content.addLayout(chorus_grid)

        # 失真
        dist_label = QLabel("失真 (Distortion)")
        dist_label.setProperty("class", "secondary")
        advanced_content.addWidget(dist_label)

        dist_grid = QGridLayout()
        dist_grid.addWidget(QLabel("驱动 (dB):"), 0, 0)
        self.dist_drive_slider = CustomSliderWidget(0, 50, 25, suffix="dB")
        dist_grid.addWidget(self.dist_drive_slider, 0, 1)

        advanced_content.addLayout(dist_grid)

        advanced_panel.add_content_layout(advanced_content)
        layout.addWidget(advanced_panel)

        # 推理参数配置面板
        inference_panel = CollapsiblePanelWidget("推理参数配置", expanded=False)

        inference_grid = QGridLayout()
        inference_grid.setSpacing(12)

        # F0提取器
        inference_grid.addWidget(QLabel("F0提取器:"), 0, 0)
        self.f0_extractor_combo = QComboBox()
        self.f0_extractor_combo.addItems([
            "rmvpe (默认)", "parselmouth", "dio", "harvest", "crepe", "fcpe"
        ])
        inference_grid.addWidget(self.f0_extractor_combo, 0, 1)

        # 共振峰偏移
        inference_grid.addWidget(QLabel("共振峰偏移:"), 1, 0)
        self.pitch_shift_slider = CustomSliderWidget(-6, 6, 0)
        inference_grid.addWidget(self.pitch_shift_slider, 1, 1)

        # 采样步数
        inference_grid.addWidget(QLabel("采样步数:"), 2, 0)
        self.sampling_steps_combo = QComboBox()
        self.sampling_steps_combo.addItems(["20", "30", "50", "100"])
        self.sampling_steps_combo.setCurrentText("50")
        self.sampling_steps_combo.setEditable(True)
        inference_grid.addWidget(self.sampling_steps_combo, 2, 1)

        # 采样器
        inference_grid.addWidget(QLabel("采样器:"), 3, 0)
        self.sampler_combo = QComboBox()
        self.sampler_combo.addItems(["euler", "rk4"])
        inference_grid.addWidget(self.sampler_combo, 3, 1)

        # 设备选择
        inference_grid.addWidget(QLabel("设备选择:"), 4, 0)
        self.device_combo = QComboBox()
        self.device_combo.addItems(["CUDA (默认)", "CPU"])
        inference_grid.addWidget(self.device_combo, 4, 1)

        inference_panel.add_content_layout(inference_grid)
        layout.addWidget(inference_panel)

    def create_api_management_tab(self):
        """创建API管理标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)

        # 创建API配置区域
        self.create_api_config_section(layout)

        # 创建控制台输出区域
        self.create_console_section(layout)

        # 创建API控制按钮
        self.create_api_control_section(layout)

        # 添加弹性空间
        layout.addStretch()

        return tab

    def create_api_config_section(self, layout):
        """创建API配置区域"""
        # 配置区域框架
        config_frame = QFrame()
        config_layout = QVBoxLayout(config_frame)
        config_layout.setContentsMargins(16, 16, 16, 16)
        config_layout.setSpacing(12)

        # 标题
        title_label = QLabel("API配置")
        title_label.setProperty("class", "title")
        config_layout.addWidget(title_label)

        # 配置网格
        config_grid = QGridLayout()
        config_grid.setSpacing(12)

        # API URL配置
        config_grid.addWidget(QLabel("API URL:"), 0, 0)
        self.api_url_input = QLineEdit()
        self.api_url_input.setText("http://127.0.0.1:9880")
        self.api_url_input.setPlaceholderText("输入API服务器地址")
        config_grid.addWidget(self.api_url_input, 0, 1, 1, 2)

        # Python环境路径
        config_grid.addWidget(QLabel("Python环境:"), 1, 0)
        self.python_path_input = QLineEdit()
        self.python_path_input.setText("workenv\\python.exe")
        self.python_path_input.setPlaceholderText("Python解释器路径")
        config_grid.addWidget(self.python_path_input, 1, 1)

        self.browse_python_btn = QPushButton("浏览...")
        self.browse_python_btn.clicked.connect(self.browse_python_path)
        config_grid.addWidget(self.browse_python_btn, 1, 2)

        # 配置选项
        options_layout = QHBoxLayout()

        self.auto_start_checkbox = QCheckBox("自动启动API")
        self.auto_start_checkbox.setChecked(True)
        options_layout.addWidget(self.auto_start_checkbox)

        self.cloud_api_checkbox = QCheckBox("云端API")
        self.cloud_api_checkbox.setChecked(True)
        options_layout.addWidget(self.cloud_api_checkbox)

        options_layout.addStretch()
        config_grid.addLayout(options_layout, 2, 0, 1, 3)

        config_layout.addLayout(config_grid)
        layout.addWidget(config_frame)

    def create_console_section(self, layout):
        """创建控制台输出区域"""
        # 控制台区域框架
        console_frame = QFrame()
        console_layout = QVBoxLayout(console_frame)
        console_layout.setContentsMargins(16, 16, 16, 16)
        console_layout.setSpacing(8)

        # 标题
        console_title = QLabel("控制台输出")
        console_title.setProperty("class", "title")
        console_layout.addWidget(console_title)

        # 控制台文本区域
        self.console_output = QTextEdit()
        self.console_output.setReadOnly(True)
        self.console_output.setMaximumHeight(200)
        self.console_output.setPlainText("等待API启动...\n")
        self.console_output.setStyleSheet("""
            QTextEdit {
                background-color: #1E1E1E;
                color: #00FF00;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                border: 1px solid #394B61;
                border-radius: 4px;
                padding: 8px;
            }
        """)
        console_layout.addWidget(self.console_output)

        layout.addWidget(console_frame)

    def create_api_control_section(self, layout):
        """创建API控制区域"""
        # 控制按钮布局
        control_layout = QHBoxLayout()

        # 启动API按钮
        self.start_api_btn = QPushButton("🚀 启动API")
        self.start_api_btn.setProperty("class", "success")
        self.start_api_btn.setMinimumHeight(45)
        self.start_api_btn.clicked.connect(self.start_api)
        control_layout.addWidget(self.start_api_btn)

        # 停止API按钮
        self.stop_api_btn = QPushButton("⏹ 停止API")
        self.stop_api_btn.setProperty("class", "danger")
        self.stop_api_btn.setMinimumHeight(45)
        self.stop_api_btn.setEnabled(False)
        self.stop_api_btn.clicked.connect(self.stop_api)
        control_layout.addWidget(self.stop_api_btn)

        # API状态标签
        self.api_status_label = QLabel("状态: 未启动")
        self.api_status_label.setAlignment(Qt.AlignCenter)
        self.api_status_label.setProperty("class", "secondary")
        control_layout.addWidget(self.api_status_label)

        layout.addLayout(control_layout)


def main():
    """主函数"""
    # 创建应用程序实例
    app = QApplication(sys.argv)

    # 设置应用程序属性
    app.setApplicationName("AI翻唱")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("AI Music Studio")

    # 应用深色主题
    style_manager.apply_dark_theme(app)

    # 创建并显示主窗口
    window = MainWindow()
    window.show()

    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
