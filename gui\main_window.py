#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI翻唱桌面应用程序 - 主窗口
基于PySide6实现，完全复刻HTML界面设计
"""

import sys
import os
from PySide6.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QSplitter, QLabel, QFrame, QTabWidget, QComboBox, QPushButton,
    QGridLayout, QListWidget, QListWidgetItem, QMenu, QMessageBox,
    QLineEdit, QTextEdit, QFileDialog, QCheckBox, QSizePolicy, QScrollArea
)
from PySide6.QtCore import Qt, QSize
from PySide6.QtGui import QFont, QPalette, QColor

# 导入样式管理器和组件
from gui.styles.style_manager import style_manager
from gui.components.file_upload_widget import FileUploadWidget
from gui.components.audio_player_widget import AudioPlayerWidget
from gui.components.collapsible_panel_widget import CollapsiblePanelWidget
from gui.components.smart_collapsible_panel import SmartCollapsiblePanel, SmartPanelGroup
from gui.components.modern_switch import ModernSwitch, ModernSwitchGroup
from gui.components.custom_slider_widget import (
    PitchSliderWidget, ReverbSliderWidget, DelaySliderWidget,
    CompressorSliderWidget, CustomSliderWidget
)
from gui.models.data_models import SongData, ProcessingStatus


class MainWindow(QMainWindow):
    """主窗口类 - 实现基本的左右分栏布局"""
    
    def __init__(self):
        super().__init__()
        self.init_ui()
        self.setup_layout()
        
    def init_ui(self):
        """初始化UI基本设置"""
        # 设置窗口标题和图标
        self.setWindowTitle("木偶AI翻唱")

        # 设置窗口大小和最小尺寸（参考MuouLiveSong项目）
        self.setMinimumSize(QSize(1000, 800))
        self.resize(1250, 1000)

        # 设置窗口居中显示
        self.center_window()
        
    def center_window(self):
        """将窗口居中显示"""
        screen = QApplication.primaryScreen().geometry()
        window = self.geometry()
        x = (screen.width() - window.width()) // 2
        y = (screen.height() - window.height()) // 2
        self.move(x, y)
        
    def setup_layout(self):
        """设置主布局 - 左右分栏 1:3 比例"""
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QHBoxLayout(central_widget)
        main_layout.setContentsMargins(0, 0, 0, 0)
        main_layout.setSpacing(0)
        
        # 创建水平分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 创建左侧面板 (1/3)
        self.left_panel = self.create_left_panel()
        splitter.addWidget(self.left_panel)
        
        # 创建右侧面板 (2/3)
        self.right_panel = self.create_right_panel()
        splitter.addWidget(self.right_panel)
        
        # 设置分割器比例 2:3 (适合1250x1000窗口)
        splitter.setSizes([500, 750])  # 左侧500px，右侧750px
        splitter.setStretchFactor(0, 2)  # 左侧拉伸因子
        splitter.setStretchFactor(1, 3)  # 右侧拉伸因子

        # 设置分割器样式和约束
        splitter.setHandleWidth(2)
        splitter.setChildrenCollapsible(False)  # 防止面板被完全折叠

        # 设置左右面板的大小策略
        self.left_panel.setSizePolicy(QSizePolicy.Preferred, QSizePolicy.Expanding)
        self.right_panel.setSizePolicy(QSizePolicy.Expanding, QSizePolicy.Expanding)
        
    def create_left_panel(self):
        """创建左侧面板"""
        # 创建主面板框架
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)

        # 创建主面板布局
        panel_layout = QVBoxLayout(panel)
        panel_layout.setContentsMargins(0, 0, 0, 0)
        panel_layout.setSpacing(0)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setFrameStyle(QFrame.NoFrame)

        # 创建滚动内容容器
        scroll_content = QWidget()
        scroll_layout = QVBoxLayout(scroll_content)
        scroll_layout.setContentsMargins(16, 16, 16, 16)
        scroll_layout.setSpacing(16)

        # 创建文件上传区域
        self.file_upload_widget = FileUploadWidget()
        self.file_upload_widget.file_selected.connect(self.on_file_selected)
        self.file_upload_widget.file_dropped.connect(self.on_file_dropped)
        scroll_layout.addWidget(self.file_upload_widget)

        # 创建音频播放器
        self.audio_player_widget = AudioPlayerWidget()
        self.audio_player_widget.playback_started.connect(self.on_playback_started)
        self.audio_player_widget.playback_paused.connect(self.on_playback_paused)
        self.audio_player_widget.playback_stopped.connect(self.on_playback_stopped)
        scroll_layout.addWidget(self.audio_player_widget)

        # 创建处理选项区域
        self.create_processing_options(scroll_layout)

        # 创建一键翻唱按钮
        self.create_main_button(scroll_layout)

        # 创建歌曲列表
        self.create_song_list(scroll_layout)

        # 添加弹性空间
        scroll_layout.addStretch()

        # 设置滚动区域内容
        scroll_area.setWidget(scroll_content)
        panel_layout.addWidget(scroll_area)

        return panel

    def create_song_list(self, layout):
        """创建歌曲列表"""
        # 创建智能折叠的歌曲列表面板
        self.song_list_panel = SmartCollapsiblePanel("我的成品歌曲", "song_list", expanded=False)

        # 创建歌曲列表控件
        self.song_list_widget = QListWidget()
        self.song_list_widget.setContextMenuPolicy(Qt.CustomContextMenu)
        self.song_list_widget.customContextMenuRequested.connect(self.show_song_context_menu)
        self.song_list_widget.setStyleSheet("""
            QListWidget {
                background-color: transparent;
                border: none;
                outline: none;
                border-radius: 8px;
            }
            QListWidget::item {
                padding: 12px 16px;
                border-bottom: 1px solid #3a3a3a;
                color: #ffffff;
                border-radius: 8px;
                margin-bottom: 2px;
            }
            QListWidget::item:hover {
                background-color: #3a3a3a;
            }
            QListWidget::item:selected {
                background-color: #007AFF;
                color: #ffffff;
            }
        """)

        # 添加到面板
        self.song_list_panel.add_content_widget(self.song_list_widget)
        layout.addWidget(self.song_list_panel)

        # 初始化歌曲数据
        self.song_data_list = []
        self.load_sample_songs()

    def load_sample_songs(self):
        """加载示例歌曲数据"""
        sample_songs = [
            "晴天 - 周杰伦.wav",
            "富士山下 - 陈奕迅.wav",
            "像我这样的人 - 毛不易.wav",
            "爱你 - 王心凌.wav",
            "稻香 - 周杰伦.wav",
            "起风了 - 吴青峰.wav",
            "孤勇者 - 陈奕迅.wav"
        ]

        for song_name in sample_songs:
            # 创建歌曲数据
            song_data = SongData(
                file_path=f"results/{song_name}",
                duration=180.0 + len(song_name) * 2  # 模拟不同时长
            )
            song_data.status = ProcessingStatus.COMPLETED

            self.song_data_list.append(song_data)
            self.add_song_to_list(song_data)

    def add_song_to_list(self, song_data: SongData):
        """添加歌曲到列表"""
        # 创建列表项
        item = QListWidgetItem()

        # 设置显示文本
        display_text = f"{song_data.get_display_name()}\n{song_data.get_status_text()} | {song_data.get_duration_text()}"
        item.setText(display_text)

        # 存储歌曲数据
        item.setData(Qt.UserRole, song_data)

        # 添加到列表
        self.song_list_widget.addItem(item)

    def add_new_song(self, file_path: str):
        """添加新歌曲"""
        song_data = SongData(file_path=file_path)
        self.song_data_list.append(song_data)
        self.add_song_to_list(song_data)

        # 展开歌曲列表面板
        if not self.song_list_panel.is_expanded:
            self.song_list_panel.expand()

    def show_song_context_menu(self, position):
        """显示歌曲右键菜单"""
        item = self.song_list_widget.itemAt(position)
        if item is None:
            return

        # 获取歌曲数据
        song_data = item.data(Qt.UserRole)
        if song_data is None:
            return

        # 创建上下文菜单
        menu = QMenu(self)
        menu.setStyleSheet("""
            QMenu {
                background-color: #293746;
                border: 1px solid #455A79;
                border-radius: 4px;
                color: #E0E0E0;
                padding: 4px;
            }
            QMenu::item {
                padding: 8px 12px;
                border-radius: 2px;
            }
            QMenu::item:selected {
                background-color: #1E88E5;
            }
            QMenu::separator {
                height: 1px;
                background-color: #455A79;
                margin: 4px 8px;
            }
        """)

        # 添加菜单项
        rename_action = menu.addAction("🏷️ 重命名")
        remix_action = menu.addAction("🎵 重新混音")
        delete_action = menu.addAction("🗑️ 删除")
        menu.addSeparator()
        open_folder_action = menu.addAction("📁 打开文件夹")

        # 连接信号
        rename_action.triggered.connect(lambda: self.rename_song(song_data))
        remix_action.triggered.connect(lambda: self.remix_song(song_data))
        delete_action.triggered.connect(lambda: self.delete_song(song_data))
        open_folder_action.triggered.connect(lambda: self.open_song_folder(song_data))

        # 显示菜单
        global_pos = self.song_list_widget.mapToGlobal(position)
        menu.exec(global_pos)

    def rename_song(self, song_data: SongData):
        """重命名歌曲"""
        print(f"重命名歌曲: {song_data.get_display_name()}")
        # TODO: 实现重命名对话框
        QMessageBox.information(self, "功能提示", f"重命名功能：{song_data.get_display_name()}")

    def remix_song(self, song_data: SongData):
        """重新混音"""
        print(f"重新混音: {song_data.get_display_name()}")
        # TODO: 实现重新混音功能
        QMessageBox.information(self, "功能提示", f"重新混音功能：{song_data.get_display_name()}")

    def delete_song(self, song_data: SongData):
        """删除歌曲"""
        print(f"删除歌曲: {song_data.get_display_name()}")

        # 确认对话框
        reply = QMessageBox.question(
            self, "确认删除",
            f"确定要删除歌曲 '{song_data.get_display_name()}' 吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )

        if reply == QMessageBox.Yes:
            # 从列表中移除
            for i in range(self.song_list_widget.count()):
                item = self.song_list_widget.item(i)
                if item.data(Qt.UserRole) == song_data:
                    self.song_list_widget.takeItem(i)
                    break

            # 从数据列表中移除
            if song_data in self.song_data_list:
                self.song_data_list.remove(song_data)

            print(f"✓ 歌曲已删除: {song_data.get_display_name()}")

    def open_song_folder(self, song_data: SongData):
        """打开歌曲文件夹"""
        print(f"打开文件夹: {song_data.file_path}")

        import subprocess
        import os

        try:
            # 获取文件所在目录
            folder_path = os.path.dirname(song_data.file_path)

            # 在Windows上打开文件夹
            if os.name == 'nt':
                subprocess.run(['explorer', folder_path])
            else:
                # Linux/Mac
                subprocess.run(['xdg-open', folder_path])

            print(f"✓ 已打开文件夹: {folder_path}")
        except Exception as e:
            print(f"✗ 打开文件夹失败: {e}")
            QMessageBox.warning(self, "错误", f"无法打开文件夹：{e}")

    def browse_python_path(self):
        """浏览Python路径"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择Python解释器",
            "",
            "可执行文件 (*.exe);;所有文件 (*.*)"
        )

        if file_path:
            self.python_path_input.setText(file_path)
            print(f"✓ Python路径已设置: {file_path}")

    def start_api(self):
        """启动API"""
        api_url = self.api_url_input.text()
        python_path = self.python_path_input.text()

        print(f"启动API: {api_url}")
        print(f"Python路径: {python_path}")

        # 更新控制台输出
        self.console_output.append(f"正在启动API服务器...")
        self.console_output.append(f"API URL: {api_url}")
        self.console_output.append(f"Python环境: {python_path}")
        self.console_output.append(f"自动启动: {'是' if self.auto_start_checkbox.isChecked() else '否'}")
        self.console_output.append(f"云端API: {'是' if self.cloud_api_checkbox.isChecked() else '否'}")
        self.console_output.append("API服务器启动成功！")

        # 更新按钮状态
        self.start_api_btn.setEnabled(False)
        self.stop_api_btn.setEnabled(True)
        self.api_status_label.setText("状态: 运行中")

        print("✓ API已启动")

    def stop_api(self):
        """停止API"""
        print("停止API")

        # 更新控制台输出
        self.console_output.append("正在停止API服务器...")
        self.console_output.append("API服务器已停止。")

        # 更新按钮状态
        self.start_api_btn.setEnabled(True)
        self.stop_api_btn.setEnabled(False)
        self.api_status_label.setText("状态: 未启动")

        print("✓ API已停止")

    def create_processing_options(self, layout):
        """创建处理选项区域"""
        # 创建选项容器
        options_frame = QFrame()
        options_layout = QGridLayout(options_frame)
        options_layout.setContentsMargins(8, 8, 8, 8)
        options_layout.setSpacing(12)

        # 处理模式选择
        mode_label = QLabel("处理模式:")
        mode_label.setProperty("class", "secondary")
        options_layout.addWidget(mode_label, 0, 0)

        self.processing_mode_combo = QComboBox()
        self.processing_mode_combo.addItems(["完整模式", "干声模式"])
        self.processing_mode_combo.setCurrentIndex(0)
        self.processing_mode_combo.currentTextChanged.connect(self.on_processing_mode_changed)
        options_layout.addWidget(self.processing_mode_combo, 0, 1)

        # 输出格式选择
        format_label = QLabel("输出格式:")
        format_label.setProperty("class", "secondary")
        options_layout.addWidget(format_label, 1, 0)

        self.output_format_combo = QComboBox()
        self.output_format_combo.addItems(["WAV", "MP3"])
        self.output_format_combo.setCurrentIndex(0)
        self.output_format_combo.currentTextChanged.connect(self.on_output_format_changed)
        options_layout.addWidget(self.output_format_combo, 1, 1)

        layout.addWidget(options_frame)

    def create_main_button(self, layout):
        """创建主要操作按钮"""
        # 一键翻唱按钮
        self.main_button = QPushButton("🚀 一键翻唱")
        self.main_button.setProperty("class", "success")
        self.main_button.setMinimumHeight(50)
        self.main_button.clicked.connect(self.on_main_button_clicked)
        layout.addWidget(self.main_button)

        # 状态标签
        self.status_label = QLabel("状态: 空闲")
        self.status_label.setAlignment(Qt.AlignCenter)
        self.status_label.setProperty("class", "secondary")
        layout.addWidget(self.status_label)

    def on_file_selected(self, file_path):
        """文件选择回调"""
        print(f"文件已通过点击选择: {file_path}")
        self.load_audio_file(file_path)

    def on_file_dropped(self, file_path):
        """文件拖拽回调"""
        print(f"文件已通过拖拽选择: {file_path}")
        self.load_audio_file(file_path)

    def load_audio_file(self, file_path):
        """加载音频文件到播放器"""
        if self.audio_player_widget.load_file(file_path):
            print(f"✓ 音频文件已加载到播放器: {file_path}")
            # 添加到歌曲列表（如果不存在）
            if not any(song.file_path == file_path for song in self.song_data_list):
                self.add_new_song(file_path)
        else:
            print(f"✗ 音频文件加载失败: {file_path}")

    def on_playback_started(self):
        """播放开始回调"""
        print("▶ 音频播放开始")

    def on_playback_paused(self):
        """播放暂停回调"""
        print("⏸ 音频播放暂停")

    def on_playback_stopped(self):
        """播放停止回调"""
        print("⏹ 音频播放停止")

    def on_processing_mode_changed(self, mode):
        """处理模式变化回调"""
        print(f"处理模式已切换为: {mode}")

    def on_output_format_changed(self, format):
        """输出格式变化回调"""
        print(f"输出格式已切换为: {format}")

    def on_voice_model_changed(self, model_name):
        """音色模型变化回调 - 自动匹配配置文件"""
        print(f"音色模型已切换为: {model_name}")

        # 提取模型名称（去掉.pt扩展名）
        model_base_name = model_name.replace('.pt', '')

        # 查找对应的配置文件
        config_name = f"{model_base_name}.yaml"

        # 在配置文件下拉框中查找并选择对应项
        config_index = self.config_file_combo.findText(config_name)
        if config_index >= 0:
            self.config_file_combo.setCurrentIndex(config_index)
            print(f"✓ 自动选择配置文件: {config_name}")
        else:
            print(f"⚠ 未找到对应的配置文件: {config_name}")

    def on_main_button_clicked(self):
        """一键翻唱按钮点击回调"""
        print("🚀 一键翻唱按钮被点击")
        if hasattr(self, 'audio_player_widget') and self.audio_player_widget.current_file:
            mode = self.processing_mode_combo.currentText()
            format = self.output_format_combo.currentText()
            print(f"  - 处理模式: {mode}")
            print(f"  - 输出格式: {format}")
            print(f"  - 音频文件: {self.audio_player_widget.current_file}")
            self.status_label.setText("状态: 处理中...")
        else:
            print("  - 请先选择音频文件")
            self.status_label.setText("状态: 请先选择音频文件")
        
    def create_right_panel(self):
        """创建右侧面板 - 包含标签页导航"""
        panel = QFrame()
        panel.setFrameStyle(QFrame.StyledPanel)

        layout = QVBoxLayout(panel)
        layout.setContentsMargins(8, 8, 8, 8)
        layout.setSpacing(0)

        # 创建标签页控件
        self.tab_widget = QTabWidget()
        layout.addWidget(self.tab_widget)

        # 创建歌曲制作标签页
        self.song_production_tab = self.create_song_production_tab()
        self.tab_widget.addTab(self.song_production_tab, "歌曲制作")

        # 创建API管理标签页
        self.api_management_tab = self.create_api_management_tab()
        self.tab_widget.addTab(self.api_management_tab, "API管理")

        return panel

    def create_song_production_tab(self):
        """创建歌曲制作标签页"""
        tab = QWidget()
        tab_layout = QVBoxLayout(tab)
        tab_layout.setContentsMargins(0, 0, 0, 0)
        tab_layout.setSpacing(0)

        # 创建滚动区域
        scroll_area = QScrollArea()
        scroll_area.setWidgetResizable(True)
        scroll_area.setHorizontalScrollBarPolicy(Qt.ScrollBarAlwaysOff)
        scroll_area.setVerticalScrollBarPolicy(Qt.ScrollBarAsNeeded)
        scroll_area.setFrameStyle(QFrame.NoFrame)

        # 创建滚动内容容器
        scroll_content = QWidget()
        layout = QVBoxLayout(scroll_content)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(12)

        # 创建智能面板组（互斥展开）
        self.right_panel_group = SmartPanelGroup(exclusive_mode=True)

        # 创建模型与音高配置区域
        self.create_model_pitch_section(layout)

        # 创建参数配置面板
        self.create_parameter_panels(layout)

        # 添加弹性空间
        layout.addStretch()

        # 设置滚动区域内容
        scroll_area.setWidget(scroll_content)
        tab_layout.addWidget(scroll_area)

        return tab

    def create_model_pitch_section(self, layout):
        """创建模型与音高配置区域"""
        # 创建智能折叠的模型配置面板，默认展开
        model_panel = SmartCollapsiblePanel("模型与音高", "model_pitch", expanded=True)
        self.right_panel_group.add_panel(model_panel)

        # 模型选择网格
        model_grid = QGridLayout()
        model_grid.setSpacing(16)

        # 第一行：音色模型和配置文件并排
        model_row_layout = QHBoxLayout()
        model_row_layout.setSpacing(16)

        # 音色模型部分
        voice_model_layout = QVBoxLayout()
        voice_model_layout.setSpacing(8)

        voice_label = QLabel("音色模型:")
        voice_label.setProperty("class", "control")
        voice_model_layout.addWidget(voice_label)

        self.voice_model_combo = QComboBox()
        self.voice_model_combo.addItems(["YSML.pt", "于洋.pt", "山海.pt"])
        self.voice_model_combo.currentTextChanged.connect(self.on_voice_model_changed)
        voice_model_layout.addWidget(self.voice_model_combo)
        model_row_layout.addLayout(voice_model_layout)

        # 配置文件部分
        config_file_layout = QVBoxLayout()
        config_file_layout.setSpacing(8)

        config_label = QLabel("配置文件:")
        config_label.setProperty("class", "control")
        config_file_layout.addWidget(config_label)

        self.config_file_combo = QComboBox()
        self.config_file_combo.addItems(["YSML.yaml", "于洋.yaml", "山海.yaml"])
        config_file_layout.addWidget(self.config_file_combo)
        model_row_layout.addLayout(config_file_layout)

        model_grid.addLayout(model_row_layout, 0, 0, 1, 2)

        # 人声音高
        pitch_label = QLabel("人声音高:")
        pitch_label.setProperty("class", "control")
        model_grid.addWidget(pitch_label, 1, 0)
        self.human_pitch_slider = PitchSliderWidget()
        model_grid.addWidget(self.human_pitch_slider, 1, 1)

        # 伴奏音高
        inst_label = QLabel("伴奏音高:")
        inst_label.setProperty("class", "control")
        model_grid.addWidget(inst_label, 2, 0)
        self.instrumental_pitch_slider = PitchSliderWidget()
        model_grid.addWidget(self.instrumental_pitch_slider, 2, 1)

        model_panel.add_content_layout(model_grid)
        layout.addWidget(model_panel)

    def create_parameter_panels(self, layout):
        """创建参数配置面板"""
        # 混响与和声面板
        reverb_panel = SmartCollapsiblePanel("混响与和声", "reverb_harmony", expanded=False)
        self.right_panel_group.add_panel(reverb_panel)

        # 创建混响开关组
        reverb_switches = ModernSwitchGroup()
        self.reverb_enable_switch = reverb_switches.add_switch("enable", "启用混响", True)
        self.harmony_to_accompaniment_switch = reverb_switches.add_switch("harmony", "和声加入伴奏", False)

        reverb_panel.add_content_widget(reverb_switches)

        # 混响参数
        reverb_grid = QGridLayout()
        reverb_grid.setSpacing(12)

        # 使用更紧凑的布局 - 2列变4列
        room_label = QLabel("房间大小:")
        room_label.setProperty("class", "control")
        reverb_grid.addWidget(room_label, 0, 0)
        self.room_size_slider = ReverbSliderWidget(0.0, 1.0, 0.5)
        reverb_grid.addWidget(self.room_size_slider, 0, 1)

        damping_label = QLabel("阻尼:")
        damping_label.setProperty("class", "control")
        reverb_grid.addWidget(damping_label, 0, 2)
        self.reverb_damping_slider = ReverbSliderWidget(0.0, 1.0, 0.5)
        reverb_grid.addWidget(self.reverb_damping_slider, 0, 3)

        width_label = QLabel("宽度:")
        width_label.setProperty("class", "control")
        reverb_grid.addWidget(width_label, 1, 0)
        self.reverb_width_slider = ReverbSliderWidget(0.0, 1.0, 1.0)
        reverb_grid.addWidget(self.reverb_width_slider, 1, 1)

        wet_label = QLabel("湿润度:")
        wet_label.setProperty("class", "control")
        reverb_grid.addWidget(wet_label, 1, 2)
        self.wet_level_slider = ReverbSliderWidget(0.0, 1.0, 0.33)
        reverb_grid.addWidget(self.wet_level_slider, 1, 3)

        dry_label = QLabel("干燥度:")
        dry_label.setProperty("class", "control")
        reverb_grid.addWidget(dry_label, 2, 0)
        self.dry_level_slider = ReverbSliderWidget(0.0, 1.0, 0.4)
        reverb_grid.addWidget(self.dry_level_slider, 2, 1)

        delay_label = QLabel("延迟:")
        delay_label.setProperty("class", "control")
        reverb_grid.addWidget(delay_label, 2, 2)
        self.delay_slider = DelaySliderWidget()
        reverb_grid.addWidget(self.delay_slider, 2, 3)

        reverb_panel.add_content_layout(reverb_grid)
        layout.addWidget(reverb_panel)

        # 高级音频参数面板
        self.create_advanced_audio_panel(layout)

        # 推理参数面板
        self.create_inference_panel(layout)

    def create_advanced_audio_panel(self, layout):
        """创建高级音频参数面板"""
        audio_panel = SmartCollapsiblePanel("高级音频参数", "advanced_audio", expanded=False)
        self.right_panel_group.add_panel(audio_panel)

        # 音频处理开关
        audio_switches = ModernSwitchGroup()
        self.noise_reduction_switch = audio_switches.add_switch("noise_reduction", "降噪处理", True)
        self.auto_gain_switch = audio_switches.add_switch("auto_gain", "自动增益", False)
        self.stereo_enhance_switch = audio_switches.add_switch("stereo_enhance", "立体声增强", True)

        audio_panel.add_content_widget(audio_switches)

        # 音频参数
        audio_grid = QGridLayout()
        audio_grid.setSpacing(12)

        # 压缩器参数
        comp_label = QLabel("压缩器:")
        comp_label.setProperty("class", "control")
        audio_grid.addWidget(comp_label, 0, 0)
        self.compressor_slider = CompressorSliderWidget()
        audio_grid.addWidget(self.compressor_slider, 0, 1)

        # 均衡器参数
        eq_label = QLabel("均衡器:")
        eq_label.setProperty("class", "control")
        audio_grid.addWidget(eq_label, 0, 2)
        self.equalizer_slider = CustomSliderWidget(minimum=0.0, maximum=1.0, value=0.5, step=0.01, decimals=2)
        audio_grid.addWidget(self.equalizer_slider, 0, 3)

        # 音量参数
        volume_label = QLabel("输出音量:")
        volume_label.setProperty("class", "control")
        audio_grid.addWidget(volume_label, 1, 0)
        self.output_volume_slider = CustomSliderWidget(minimum=0.0, maximum=1.0, value=0.8, step=0.01, decimals=2)
        audio_grid.addWidget(self.output_volume_slider, 1, 1)

        # 采样率
        sample_label = QLabel("采样率:")
        sample_label.setProperty("class", "control")
        audio_grid.addWidget(sample_label, 1, 2)
        self.sample_rate_combo = QComboBox()
        self.sample_rate_combo.addItems(["44100 Hz", "48000 Hz", "96000 Hz"])
        self.sample_rate_combo.setCurrentIndex(1)
        audio_grid.addWidget(self.sample_rate_combo, 1, 3)

        audio_panel.add_content_layout(audio_grid)
        layout.addWidget(audio_panel)

    def create_inference_panel(self, layout):
        """创建推理参数面板"""
        inference_panel = SmartCollapsiblePanel("推理参数", "inference", expanded=False)
        self.right_panel_group.add_panel(inference_panel)

        # 推理开关
        inference_switches = ModernSwitchGroup()
        self.gpu_acceleration_switch = inference_switches.add_switch("gpu_accel", "GPU加速", True)
        self.batch_processing_switch = inference_switches.add_switch("batch_proc", "批量处理", False)
        self.real_time_switch = inference_switches.add_switch("real_time", "实时处理", False)

        inference_panel.add_content_widget(inference_switches)

        # 推理参数
        inference_grid = QGridLayout()
        inference_grid.setSpacing(12)

        # 批次大小
        batch_label = QLabel("批次大小:")
        batch_label.setProperty("class", "control")
        inference_grid.addWidget(batch_label, 0, 0)
        self.batch_size_combo = QComboBox()
        self.batch_size_combo.addItems(["1", "2", "4", "8", "16"])
        self.batch_size_combo.setCurrentIndex(2)
        inference_grid.addWidget(self.batch_size_combo, 0, 1)

        # 推理精度
        precision_label = QLabel("推理精度:")
        precision_label.setProperty("class", "control")
        inference_grid.addWidget(precision_label, 0, 2)
        self.precision_combo = QComboBox()
        self.precision_combo.addItems(["FP32", "FP16", "INT8"])
        self.precision_combo.setCurrentIndex(1)
        inference_grid.addWidget(self.precision_combo, 0, 3)

        # 线程数
        thread_label = QLabel("线程数:")
        thread_label.setProperty("class", "control")
        inference_grid.addWidget(thread_label, 1, 0)
        self.thread_count_slider = CustomSliderWidget(minimum=1, maximum=16, value=4, step=1, decimals=0)
        inference_grid.addWidget(self.thread_count_slider, 1, 1)

        # 缓存大小
        cache_label = QLabel("缓存大小:")
        cache_label.setProperty("class", "control")
        inference_grid.addWidget(cache_label, 1, 2)
        self.cache_size_combo = QComboBox()
        self.cache_size_combo.addItems(["512MB", "1GB", "2GB", "4GB"])
        self.cache_size_combo.setCurrentIndex(1)
        inference_grid.addWidget(self.cache_size_combo, 1, 3)

        inference_panel.add_content_layout(inference_grid)
        layout.addWidget(inference_panel)

        # 高级音频参数面板
        advanced_panel = CollapsiblePanelWidget("高级音频参数", expanded=False)

        # 重新创建带复选框的标题栏
        advanced_panel.header_widget.setParent(None)
        advanced_panel.header_widget = advanced_panel.create_header_with_checkboxes(
            "高级音频参数",
            [("启用", False)]
        )
        advanced_panel.layout().insertWidget(0, advanced_panel.header_widget)

        # 高级参数内容
        advanced_content = QVBoxLayout()

        # 压缩器
        comp_label = QLabel("压缩器 (Compressor)")
        comp_label.setProperty("class", "secondary")
        advanced_content.addWidget(comp_label)

        comp_grid = QGridLayout()
        comp_grid.addWidget(QLabel("阈值 (dB):"), 0, 0)
        self.comp_threshold_slider = CompressorSliderWidget()
        comp_grid.addWidget(self.comp_threshold_slider, 0, 1)

        comp_grid.addWidget(QLabel("比率:"), 1, 0)
        self.comp_ratio_slider = CustomSliderWidget(1.0, 20.0, 4.0, decimals=1)
        comp_grid.addWidget(self.comp_ratio_slider, 1, 1)

        advanced_content.addLayout(comp_grid)

        # 合唱
        chorus_label = QLabel("合唱 (Chorus)")
        chorus_label.setProperty("class", "secondary")
        advanced_content.addWidget(chorus_label)

        chorus_grid = QGridLayout()
        chorus_grid.addWidget(QLabel("速率 (Hz):"), 0, 0)
        self.chorus_rate_slider = CustomSliderWidget(0.1, 10.0, 1.0, decimals=1, suffix="Hz")
        chorus_grid.addWidget(self.chorus_rate_slider, 0, 1)

        chorus_grid.addWidget(QLabel("深度:"), 1, 0)
        self.chorus_depth_slider = ReverbSliderWidget(0.0, 1.0, 0.25)
        chorus_grid.addWidget(self.chorus_depth_slider, 1, 1)

        advanced_content.addLayout(chorus_grid)

        # 失真
        dist_label = QLabel("失真 (Distortion)")
        dist_label.setProperty("class", "secondary")
        advanced_content.addWidget(dist_label)

        dist_grid = QGridLayout()
        dist_grid.addWidget(QLabel("驱动 (dB):"), 0, 0)
        self.dist_drive_slider = CustomSliderWidget(0, 50, 25, suffix="dB")
        dist_grid.addWidget(self.dist_drive_slider, 0, 1)

        advanced_content.addLayout(dist_grid)

        advanced_panel.add_content_layout(advanced_content)
        layout.addWidget(advanced_panel)

        # 推理参数配置面板
        inference_panel = CollapsiblePanelWidget("推理参数配置", expanded=False)

        inference_grid = QGridLayout()
        inference_grid.setSpacing(12)

        # F0提取器
        inference_grid.addWidget(QLabel("F0提取器:"), 0, 0)
        self.f0_extractor_combo = QComboBox()
        self.f0_extractor_combo.addItems([
            "rmvpe (默认)", "parselmouth", "dio", "harvest", "crepe", "fcpe"
        ])
        inference_grid.addWidget(self.f0_extractor_combo, 0, 1)

        # 共振峰偏移
        inference_grid.addWidget(QLabel("共振峰偏移:"), 1, 0)
        self.pitch_shift_slider = CustomSliderWidget(-6, 6, 0)
        inference_grid.addWidget(self.pitch_shift_slider, 1, 1)

        # 采样步数
        inference_grid.addWidget(QLabel("采样步数:"), 2, 0)
        self.sampling_steps_combo = QComboBox()
        self.sampling_steps_combo.addItems(["20", "30", "50", "100"])
        self.sampling_steps_combo.setCurrentText("50")
        self.sampling_steps_combo.setEditable(True)
        inference_grid.addWidget(self.sampling_steps_combo, 2, 1)

        # 采样器
        inference_grid.addWidget(QLabel("采样器:"), 3, 0)
        self.sampler_combo = QComboBox()
        self.sampler_combo.addItems(["euler", "rk4"])
        inference_grid.addWidget(self.sampler_combo, 3, 1)

        # 设备选择
        inference_grid.addWidget(QLabel("设备选择:"), 4, 0)
        self.device_combo = QComboBox()
        self.device_combo.addItems(["CUDA (默认)", "CPU"])
        inference_grid.addWidget(self.device_combo, 4, 1)

        inference_panel.add_content_layout(inference_grid)
        layout.addWidget(inference_panel)

    def create_api_management_tab(self):
        """创建API管理标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)
        layout.setContentsMargins(16, 16, 16, 16)
        layout.setSpacing(16)

        # 创建API配置区域
        self.create_api_config_section(layout)

        # 创建控制台输出区域
        self.create_console_section(layout)

        # 创建API控制按钮
        self.create_api_control_section(layout)

        # 添加弹性空间
        layout.addStretch()

        return tab

    def create_api_config_section(self, layout):
        """创建API配置区域"""
        # 配置区域框架
        config_frame = QFrame()
        config_layout = QVBoxLayout(config_frame)
        config_layout.setContentsMargins(16, 16, 16, 16)
        config_layout.setSpacing(12)

        # 标题
        title_label = QLabel("API配置")
        title_label.setProperty("class", "title")
        config_layout.addWidget(title_label)

        # 配置网格
        config_grid = QGridLayout()
        config_grid.setSpacing(12)

        # API URL配置
        config_grid.addWidget(QLabel("API URL:"), 0, 0)
        self.api_url_input = QLineEdit()
        self.api_url_input.setText("http://127.0.0.1:9880")
        self.api_url_input.setPlaceholderText("输入API服务器地址")
        config_grid.addWidget(self.api_url_input, 0, 1, 1, 2)

        # Python环境路径
        config_grid.addWidget(QLabel("Python环境:"), 1, 0)
        self.python_path_input = QLineEdit()
        self.python_path_input.setText("workenv\\python.exe")
        self.python_path_input.setPlaceholderText("Python解释器路径")
        config_grid.addWidget(self.python_path_input, 1, 1)

        self.browse_python_btn = QPushButton("浏览...")
        self.browse_python_btn.clicked.connect(self.browse_python_path)
        config_grid.addWidget(self.browse_python_btn, 1, 2)

        # 配置选项
        options_layout = QHBoxLayout()

        self.auto_start_checkbox = QCheckBox("自动启动API")
        self.auto_start_checkbox.setChecked(True)
        options_layout.addWidget(self.auto_start_checkbox)

        self.cloud_api_checkbox = QCheckBox("云端API")
        self.cloud_api_checkbox.setChecked(True)
        options_layout.addWidget(self.cloud_api_checkbox)

        options_layout.addStretch()
        config_grid.addLayout(options_layout, 2, 0, 1, 3)

        config_layout.addLayout(config_grid)
        layout.addWidget(config_frame)

    def create_console_section(self, layout):
        """创建控制台输出区域"""
        # 控制台区域框架
        console_frame = QFrame()
        console_layout = QVBoxLayout(console_frame)
        console_layout.setContentsMargins(16, 16, 16, 16)
        console_layout.setSpacing(8)

        # 标题
        console_title = QLabel("控制台输出")
        console_title.setProperty("class", "title")
        console_layout.addWidget(console_title)

        # 控制台文本区域
        self.console_output = QTextEdit()
        self.console_output.setReadOnly(True)
        self.console_output.setMaximumHeight(200)
        self.console_output.setPlainText("等待API启动...\n")
        self.console_output.setStyleSheet("""
            QTextEdit {
                background-color: #1E1E1E;
                color: #00FF00;
                font-family: 'Consolas', 'Monaco', monospace;
                font-size: 12px;
                border: 1px solid #394B61;
                border-radius: 4px;
                padding: 8px;
            }
        """)
        console_layout.addWidget(self.console_output)

        layout.addWidget(console_frame)

    def create_api_control_section(self, layout):
        """创建API控制区域"""
        # 控制按钮布局
        control_layout = QHBoxLayout()

        # 启动API按钮
        self.start_api_btn = QPushButton("🚀 启动API")
        self.start_api_btn.setProperty("class", "success")
        self.start_api_btn.setMinimumHeight(45)
        self.start_api_btn.clicked.connect(self.start_api)
        control_layout.addWidget(self.start_api_btn)

        # 停止API按钮
        self.stop_api_btn = QPushButton("⏹ 停止API")
        self.stop_api_btn.setProperty("class", "danger")
        self.stop_api_btn.setMinimumHeight(45)
        self.stop_api_btn.setEnabled(False)
        self.stop_api_btn.clicked.connect(self.stop_api)
        control_layout.addWidget(self.stop_api_btn)

        # API状态标签
        self.api_status_label = QLabel("状态: 未启动")
        self.api_status_label.setAlignment(Qt.AlignCenter)
        self.api_status_label.setProperty("class", "secondary")
        control_layout.addWidget(self.api_status_label)

        layout.addLayout(control_layout)


def main():
    """主函数"""
    # 启用高DPI支持
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)

    # 创建应用程序实例
    app = QApplication(sys.argv)

    # 设置应用程序属性
    app.setApplicationName("木偶AI翻唱")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("AI Music Studio")

    # 应用深色主题
    style_manager.apply_dark_theme(app)

    # 创建并显示主窗口
    window = MainWindow()
    window.show()

    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
