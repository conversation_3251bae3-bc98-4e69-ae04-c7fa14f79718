#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进布局测试脚本 - 测试滚动区域和紧凑布局
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from PySide6.QtWidgets import QApplication
from PySide6.QtCore import Qt
from gui.main_window import MainWindow
from gui.styles.style_manager import style_manager


def main():
    """主函数"""
    # 启用高DPI支持
    if hasattr(Qt, 'AA_EnableHighDpiScaling'):
        QApplication.setAttribute(Qt.AA_EnableHighDpiScaling, True)
    if hasattr(Qt, 'AA_UseHighDpiPixmaps'):
        QApplication.setAttribute(Qt.AA_UseHighDpiPixmaps, True)
    
    # 创建应用程序实例
    app = QApplication(sys.argv)
    
    # 设置应用程序属性
    app.setApplicationName("AI翻唱 - 改进布局测试")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("AI Music Studio")
    
    # 应用深色主题
    style_manager.apply_dark_theme(app)
    
    # 创建并显示主窗口
    window = MainWindow()
    window.show()
    
    print("🎵 改进布局测试启动成功")
    print("📋 改进内容:")
    print("   ✅ 左侧面板添加滚动区域")
    print("   ✅ 右侧面板添加滚动区域")
    print("   ✅ 混响参数使用紧凑4列布局")
    print("   ✅ 响应式字体和控件大小")
    print("   ✅ 优化的滚动条样式")
    print("🔧 测试要点:")
    print("   - 展开所有可折叠面板查看滚动效果")
    print("   - 调整窗口大小测试响应式效果")
    print("   - 检查混响参数的紧凑布局")
    print("   - 验证滚动条的外观和功能")
    
    # 运行应用程序
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
