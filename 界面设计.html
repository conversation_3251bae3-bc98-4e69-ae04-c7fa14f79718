<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="utf-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>AI翻唱</title>
    <link href="https://cdn.jsdelivr.net/npm/tailwindcss@2.2.19/dist/tailwind.min.css" rel="stylesheet"/>
    <link href="https://fonts.googleapis.com/icon?family=Material+Icons" rel="stylesheet"/>
    <style>
        /* 样式参考自 main_window.py 中的 PySide6 样式表 */
        body {
            font-family: 'Roboto', sans-serif;
            background-color: #1E2A3A; /* QMainWindow background */
            color: #E5E7EB;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            min-height: 100vh;
            padding-top: 2rem;
            padding-bottom: 2rem;
        }
        .main-container {
            width: 100%;
            max-width: 900px;
            min-height: 600px;
        }
        /* 标签页容器 */
        .tabs {
            border-bottom: 1px solid #394B61;
        }
        /* 标签按钮 */
        .tab-button {
            background-color: #293746; /* QTabBar::tab background */
            color: #B0BEC5; /* QTabBar::tab color */
            padding: 10px 20px;
            cursor: pointer;
            transition: all 0.2s ease-in-out;
            border: 1px solid transparent;
            border-bottom: none;
            border-top-left-radius: 4px;
            border-top-right-radius: 4px;
        }
        .tab-button:hover {
            background-color: #455A79;
        }
        /* 激活状态的标签按钮 */
        .tab-button.active {
            background-color: #394B61; /* QTabBar::tab:selected background */
            color: white;
            border-color: #394B61;
        }
        /* 标签内容面板 */
        .tab-content {
            display: none; /* 默认隐藏 */
        }
        .tab-content.active {
            display: block; /* 显示激活的面板 */
        }
        /* 卡片/面板样式，模仿 QGroupBox */
        .card {
            background-color: #293746; /* 类似 Frame 背景 */
            border: 1px solid #394B61;
            border-radius: 4px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
        }
        /* 输入框样式 */
        .input-field {
            background-color: #394B61; /* QComboBox background */
            border: 1px solid #4B5563;
            border-radius: 4px;
            padding: 8px 12px;
            color: #E5E7EB;
            transition: border-color 0.2s ease-in-out;
        }
        .input-field:focus {
            border-color: #1E88E5; /* 蓝色高亮 */
            outline: none;
        }
        /* 滑块样式 */
        .slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 16px;
            height: 16px;
            background-color: #1E88E5; /* 蓝色滑块 */
            border-radius: 50%;
            cursor: pointer;
        }
        .slider-track {
            height: 8px;
            background: #394B61;
            border-radius: 4px;
        }
        /* 按钮通用样式 */
        .btn {
            color: white;
            font-weight: 600;
            padding: 10px 20px;
            border-radius: 4px;
            transition: background-color 0.2s ease-in-out;
            display: flex;
            align-items: center;
            justify-content: center;
            border: none;
        }
        /* 绿色按钮 (成功/启动) */
        .btn-green {
            background-color: #4CAF50;
        }
        .btn-green:hover {
            background-color: #66BB6A;
        }
        /* 红色按钮 (危险/停止) */
        .btn-red {
            background-color: #F44336;
        }
        .btn-red:hover {
            background-color: #EF5350;
        }
        /* 灰色按钮 (次要操作) */
        .btn-gray {
            background-color: #394B61;
        }
        .btn-gray:hover {
            background-color: #455A79;
        }
        /* 标题和标签文本 */
        .label-text {
            font-size: 0.875rem;
            color: #B0BEC5;
            margin-bottom: 0.5rem;
        }
        .value-text {
            font-size: 0.875rem;
            color: #D1D5DB;
        }
        /* 可折叠容器样式 */
        .collapsible-container {
             max-height: 0;
             overflow: hidden;
             transition: max-height 0.5s ease-in-out;
        }
        .song-list {
            max-height: 220px;
            overflow-y: auto;
        }
        .song-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px;
            border-bottom: 1px solid #394B61;
            cursor: pointer;
            transition: background-color 0.2s;
        }
        .song-item:hover {
            background-color: #394B61;
        }
        .song-item-menu-btn {
            background: transparent;
            border: none;
            color: #B0BEC5;
            padding: 4px;
            border-radius: 50%;
        }
        .song-item-menu-btn:hover {
            background-color: #455A79;
        }
        /* 自定义滚动条 */
        .song-list::-webkit-scrollbar {
          width: 8px;
        }
        .song-list::-webkit-scrollbar-track {
          background: #293746;
        }
        .song-list::-webkit-scrollbar-thumb {
          background-color: #455A79;
          border-radius: 4px;
          border: 2px solid #293746;
        }
        /* 上下文菜单样式 */
        .context-menu {
            position: absolute; z-index: 1000; display: none;
            background-color: #293746; border: 1px solid #455A79;
            border-radius: 4px; box-shadow: 0 5px 15px rgba(0,0,0,0.3); min-width: 150px;
        }
        .context-menu-item {
            padding: 8px 12px; color: #E0E0E0; cursor: pointer;
            display: flex; align-items: center; gap: 8px;
        }
        .context-menu-item:hover { background-color: #1E88E5; }
        /* 文件拖拽区域 */
        .drop-zone {
            position: relative;
        }
        .drop-zone.drag-over {
            border-color: #3B82F6;
            background-color: rgba(59, 130, 246, 0.1);
        }
        .drop-zone input[type="file"] {
            position: absolute;
            top: 0; left: 0; width: 100%; height: 100%;
            opacity: 0; cursor: pointer;
        }
         /* 播放器样式 */
        .audio-player {
            display: none;
            padding: 1rem;
            margin-top: 1rem;
            background-color: #394B61;
            border-radius: 8px;
        }
        .progress-bar-container {
            width: 100%; background-color: #293746; border-radius: 4px;
            height: 8px; cursor: pointer;
        }
        .progress-bar {
            background-color: #1E88E5; height: 100%;
            width: 0%; border-radius: 4px;
        }
    </style>
</head>
<body>

    <div class="main-container">
        <!-- 标签页导航 -->
        <div class="tabs flex items-center mb-6">
            <button class="tab-button active" onclick="openTab(event, 'song-production')">歌曲制作</button>
            <button class="tab-button" onclick="openTab(event, 'api-management')">API管理</button>
        </div>

        <!-- 上下文菜单 (全局) -->
        <div id="song-context-menu" class="context-menu">
             <div class="context-menu-item"><span class="material-icons text-base">drive_file_rename_outline</span>重命名</div>
             <div class="context-menu-item"><span class="material-icons text-base">graphic_eq</span>重新混音</div>
             <div class="context-menu-item"><span class="material-icons text-base">delete</span>删除</div>
             <hr class="border-gray-600 my-1">
             <div class="context-menu-item"><span class="material-icons text-base">folder_open</span>打开文件夹</div>
        </div>

        <!-- 标签页内容 -->
        <div id="tab-content-container">
            <!-- 歌曲制作 (默认显示) -->
            <div id="song-production" class="tab-content active">
                <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 lg:gap-8">
                    <!-- Left Column -->
                    <div class="lg:col-span-1 space-y-6 lg:space-y-8">
                         <!-- 上传和处理 -->
                        <div class="card p-6">
                            <div id="drop-zone" class="drop-zone flex flex-col items-center justify-center p-8 border-2 border-dashed border-blue-500 rounded-lg cursor-pointer hover:bg-gray-700/50 transition-colors">
                                <input type="file" id="file-input" accept="audio/*">
                                <span class="material-icons text-6xl text-blue-400 mb-3 pointer-events-none">upload_file</span>
                                <p id="drop-zone-text" class="text-blue-300 text-center pointer-events-none">在此处选择或拖拽音频文件</p>
                            </div>
                            <!-- 音频播放器 -->
                            <div id="audio-player" class="audio-player">
                                <p id="file-name" class="text-sm font-semibold truncate mb-2"></p>
                                <div class="flex items-center gap-4">
                                    <button id="play-pause-btn" class="p-2 rounded-full bg-blue-600 hover:bg-blue-500">
                                        <span class="material-icons text-white">play_arrow</span>
                                    </button>
                                    <div id="progress-bar-container" class="progress-bar-container">
                                        <div id="progress-bar" class="progress-bar"></div>
                                    </div>
                                    <span id="time-display" class="text-xs w-20 text-center">0:00 / 0:00</span>
                                </div>
                                <audio id="audio-element" class="hidden"></audio>
                            </div>
                            <div class="mt-6 grid grid-cols-1 md:grid-cols-2 gap-4">
                                <div>
                                    <label class="label-text block" for="processing-mode">处理模式:</label>
                                    <select class="input-field w-full" id="processing-mode">
                                        <option>完整模式</option>
                                        <option>干声模式</option>
                                    </select>
                                </div>
                                <div>
                                    <label class="label-text block" for="output-format">输出格式:</label>
                                    <select class="input-field w-full" id="output-format">
                                        <option>WAV</option>
                                        <option>MP3</option>
                                    </select>
                                </div>
                            </div>
                             <div class="mt-8">
                                 <button class="btn btn-green w-full text-lg">
                                    <span class="material-icons mr-2">rocket_launch</span>
                                    一键翻唱
                                 </button>
                                <p class="text-center mt-4 text-sm text-gray-400">状态: <span class="font-semibold text-gray-300">空闲</span></p>
                            </div>
                        </div>

                         <!-- 歌曲列表 (可折叠) -->
                        <div class="card">
                             <button class="collapsible-toggle-btn w-full text-left p-4 flex justify-between items-center hover:bg-gray-700/20">
                                 <span class="font-semibold text-lg">我的成品歌曲</span>
                                 <span class="material-icons transition-transform duration-300">expand_more</span>
                             </button>
                             <div class="collapsible-container">
                                 <div id="song-list" class="song-list p-2">
                                     <!-- 歌曲项将由JS动态添加 -->
                                 </div>
                             </div>
                        </div>
                    </div>

                    <!-- Right Column -->
                    <div class="lg:col-span-2 space-y-4">
                        <div class="card p-6">
                            <!-- 模型与音高 -->
                            <div class="border-b border-gray-700 pb-4">
                                <h3 class="text-lg font-semibold">模型与音高</h3>
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-4">
                                     <div><label class="label-text block" for="voice-model">音色模型:</label><select class="input-field w-full" id="voice-model"><option>YSML.pt</option></select></div>
                                     <div><label class="label-text block" for="config-file">配置文件:</label><select class="input-field w-full" id="config-file"><option>YSML.yaml</option></select></div>
                                     <div class="md:col-span-1"><label class="label-text block" for="human-pitch">人声音高:</label><div class="flex items-center space-x-3"><input class="w-full slider-track" id="human-pitch" max="12" min="-12" type="range" value="0"/><span class="value-text w-8 text-right">0</span></div></div>
                                     <div class="md:col-span-1"><label class="label-text block" for="instrumental-pitch">伴奏音高:</label><div class="flex items-center space-x-3"><input class="w-full slider-track" id="instrumental-pitch" max="12" min="-12" type="range" value="0"/><span class="value-text w-8 text-right">0</span></div></div>
                                </div>
                            </div>
                        </div>
                        <!-- 折叠区域容器 -->
                        <div class="space-y-4">
                            <!-- 混响与和声 -->
                            <div class="card bg-gray-800/50">
                                <button class="collapsible-toggle-btn w-full text-left p-4 flex justify-between items-center hover:bg-gray-700/30 rounded-md">
                                    <span class="font-semibold text-lg">混响与和声</span>
                                    <div class="flex items-center space-x-4">
                                        <label class="inline-flex items-center cursor-pointer" onclick="event.stopPropagation()"><input class="h-4 w-4 rounded border-gray-600 bg-gray-700 text-blue-500 focus:ring-blue-400" type="checkbox" checked><span class="ml-2 text-sm font-normal text-gray-300">启用混响</span></label>
                                        <label class="inline-flex items-center cursor-pointer" onclick="event.stopPropagation()"><input class="h-4 w-4 rounded border-gray-600 bg-gray-700 text-blue-500 focus:ring-blue-400" type="checkbox"><span class="ml-2 text-sm font-normal text-gray-300">和声加入伴奏</span></label>
                                        <span class="material-icons transition-transform duration-300">expand_more</span>
                                    </div>
                                </button>
                                <div class="collapsible-container">
                                    <div class="p-4 border-t border-gray-700 grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4">
                                        <div><label class="label-text block" for="room-size">房间大小:</label><div class="flex items-center space-x-3"><input class="w-full slider-track" id="room-size" max="1" min="0" step="0.01" type="range" value="0.5"/><span class="value-text w-10 text-right">0.50</span></div></div>
                                        <div><label class="label-text block" for="reverb-damping">阻尼:</label><div class="flex items-center space-x-3"><input class="w-full slider-track" id="reverb-damping" max="1" min="0" step="0.01" type="range" value="0.5"/><span class="value-text w-10 text-right">0.50</span></div></div>
                                        <div><label class="label-text block" for="reverb-width">宽度:</label><div class="flex items-center space-x-3"><input class="w-full slider-track" id="reverb-width" max="1" min="0" step="0.01" type="range" value="1.0"/><span class="value-text w-10 text-right">1.00</span></div></div>
                                        <div><label class="label-text block" for="wet-level">湿润度:</label><div class="flex items-center space-x-3"><input class="w-full slider-track" id="wet-level" max="1" min="0" step="0.01" type="range" value="0.33"/><span class="value-text w-10 text-right">0.33</span></div></div>
                                        <div><label class="label-text block" for="dry-level">干燥度:</label><div class="flex items-center space-x-3"><input class="w-full slider-track" id="dry-level" max="1" min="0" step="0.01" type="range" value="0.4"/><span class="value-text w-10 text-right">0.40</span></div></div>
                                        <div><label class="label-text block" for="delay-seconds">延迟:</label><div class="flex items-center space-x-3"><input class="w-full slider-track" id="delay-seconds" max="2" min="0" step="0.01" type="range" value="0.0"/><span class="value-text w-10 text-right">0.00s</span></div></div>
                                    </div>
                                </div>
                            </div>
                            <!-- 高级音频参数 -->
                            <div class="card bg-gray-800/50">
                               <button class="collapsible-toggle-btn w-full text-left p-4 flex justify-between items-center hover:bg-gray-700/30 rounded-md">
                                    <span class="font-semibold text-lg">高级音频参数</span>
                                     <div class="flex items-center space-x-4">
                                        <label class="inline-flex items-center cursor-pointer" onclick="event.stopPropagation()"><input class="h-4 w-4 rounded border-gray-600 bg-gray-700 text-blue-500 focus:ring-blue-400" type="checkbox"><span class="ml-2 text-sm font-normal text-gray-300">启用</span></label>
                                        <span class="material-icons transition-transform duration-300">expand_more</span>
                                    </div>
                                </button>
                                <div class="collapsible-container">
                                     <div class="p-4 border-t border-gray-700 space-y-4">
                                        <!-- Compressor -->
                                        <div class="text-gray-400">
                                            <h4 class="font-semibold text-gray-300">压缩器 (Compressor)</h4>
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 mt-2">
                                                <div><label class="label-text block" for="comp-threshold">阈值 (dB):</label><div class="flex items-center space-x-3"><input class="w-full slider-track" id="comp-threshold" max="0" min="-60" step="1" type="range" value="-20"/><span class="value-text w-10 text-right">-20</span></div></div>
                                                <div><label class="label-text block" for="comp-ratio">比率:</label><div class="flex items-center space-x-3"><input class="w-full slider-track" id="comp-ratio" max="20" min="1" step="0.1" type="range" value="4"/><span class="value-text w-10 text-right">4.0</span></div></div>
                                            </div>
                                        </div>
                                        <!-- Chorus -->
                                        <div class="text-gray-400">
                                            <h4 class="font-semibold text-gray-300 mt-4">合唱 (Chorus)</h4>
                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-x-6 gap-y-4 mt-2">
                                                <div><label class="label-text block" for="chorus-rate">速率 (Hz):</label><div class="flex items-center space-x-3"><input class="w-full slider-track" id="chorus-rate" max="10" min="0.1" step="0.1" type="range" value="1.0"/><span class="value-text w-10 text-right">1.0</span></div></div>
                                                <div><label class="label-text block" for="chorus-depth">深度:</label><div class="flex items-center space-x-3"><input class="w-full slider-track" id="chorus-depth" max="1" min="0" step="0.01" type="range" value="0.25"/><span class="value-text w-10 text-right">0.25</span></div></div>
                                            </div>
                                        </div>
                                        <!-- Distortion -->
                                        <div class="text-gray-400">
                                            <h4 class="font-semibold text-gray-300 mt-4">失真 (Distortion)</h4>
                                             <div class="mt-2">
                                                <label class="label-text block" for="dist-drive">驱动 (dB):</label>
                                                <div class="flex items-center space-x-3">
                                                     <input class="w-full slider-track" id="dist-drive" max="50" min="0" step="1" type="range" value="25"/>
                                                     <span class="value-text w-10 text-right">25</span>
                                                 </div>
                                             </div>
                                        </div>
                                     </div>
                                </div>
                            </div>
                            <!-- 推理参数配置 -->
                            <div class="card bg-gray-800/50">
                                <button class="collapsible-toggle-btn w-full text-left p-4 flex justify-between items-center hover:bg-gray-700/30 rounded-md">
                                    <span class="font-semibold text-lg">推理参数配置</span>
                                    <span class="material-icons transition-transform duration-300">expand_more</span>
                                </button>
                                <div class="collapsible-container">
                                    <div class="p-4 border-t border-gray-700 grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div><label class="label-text block" for="f0-extractor">F0提取器:</label><select class="input-field w-full" id="f0-extractor"><option>rmvpe (默认)</option><option>parselmouth</option><option>dio</option><option>harvest</option><option>crepe</option><option>fcpe</option></select></div>
                                        <div><label class="label-text block" for="pitch-shift">共振峰偏移:</label><div class="flex items-center space-x-3"><input class="w-full slider-track" id="pitch-shift" max="6" min="-6" type="range" value="0"/><span class="value-text w-8 text-right">0</span></div></div>
                                        <div><label class="label-text block" for="sampling-steps">采样步数:</label><input class="input-field w-full" id="sampling-steps" type="number" value="50"/></div>
                                        <div><label class="label-text block" for="sampler">采样器:</label><select class="input-field w-full" id="sampler"><option>euler</option><option>rk4</option></select></div>
                                        <div class="md:col-span-2"><label class="label-text block" for="device-selection">设备选择:</label><select class="input-field w-full" id="device-selection"><option>CUDA (默认)</option><option>CPU</option></select></div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- API管理 -->
            <div id="api-management" class="tab-content">
                <div class="card p-6">
                    <h2 class="section-title-container text-lg">API 配置</h2>
                    <div class="space-y-4 mb-6 mt-4">
                        <div>
                            <label class="label-text block" for="api-url">API URL:</label>
                            <input class="input-field w-full" id="api-url" type="text" value="http://127.0.0.1:9880"/>
                        </div>
                        <div>
                            <label class="label-text block" for="python-env">Python 环境:</label>
                            <div class="flex items-center gap-2">
                                <input class="input-field w-full" id="python-env" type="text" value="workenv\python.exe"/>
                                <button class="btn btn-gray whitespace-nowrap">浏览</button>
                            </div>
                        </div>
                        <label class="inline-flex items-center cursor-pointer">
                            <input class="h-4 w-4 rounded border-gray-600 bg-gray-700 text-blue-500 focus:ring-blue-400" type="checkbox" checked/>
                            <span class="ml-2 text-gray-300">随软件启动API</span>
                        </label>
                        <label class="inline-flex items-center cursor-pointer">
                            <input class="h-4 w-4 rounded border-gray-600 bg-gray-700 text-blue-500 focus:ring-blue-400" type="checkbox" checked/>
                            <span class="ml-2 text-gray-300">云端API</span>
                        </label>
                    </div>

                    <div class="space-y-4">
                       <h3 class="text-lg font-semibold text-gray-300">控制台输出</h3>
                       <div class="console-output">
                           <p>&gt; Console output will appear here...</p>
                       </div>
                       <div class="grid grid-cols-2 gap-4 mt-4">
                            <button class="btn btn-green">启动 API</button>
                            <button class="btn btn-red">停止 API</button>
                       </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // --- Tab Functionality ---
        function openTab(evt, tabName) {
            document.querySelectorAll('.tab-content').forEach(tc => tc.style.display = 'none');
            document.querySelectorAll('.tab-button').forEach(tb => tb.classList.remove('active'));
            document.getElementById(tabName).style.display = 'block';
            evt.currentTarget.classList.add('active');
        }

        // --- Slider Value Update ---
        document.querySelectorAll('input[type="range"]').forEach(slider => {
            const valueSpan = slider.parentElement.querySelector('.value-text');
            if (valueSpan) {
                const updateValue = () => {
                    let value;
                    if (['room-size', 'reverb-damping', 'reverb-width', 'wet-level', 'dry-level', 'chorus-depth', 'delay-seconds'].includes(slider.id)) {
                        value = parseFloat(slider.value).toFixed(2);
                        if (slider.id === 'delay-seconds') value += 's';
                    } else if (['chorus-rate', 'comp-ratio'].includes(slider.id)) {
                        value = parseFloat(slider.value).toFixed(1);
                    } else { value = slider.value; }
                    valueSpan.textContent = value;
                };
                updateValue();
                slider.addEventListener('input', updateValue);
            }
        });
        
        // --- Universal Collapsible Container Logic ---
        document.querySelectorAll('.collapsible-toggle-btn').forEach(btn => {
            btn.addEventListener('click', () => {
                const container = btn.nextElementSibling;
                const arrowIcon = btn.querySelector('.material-icons');
                if (container.style.maxHeight && container.style.maxHeight !== '0px') {
                    container.style.maxHeight = '0px';
                    if(arrowIcon) arrowIcon.style.transform = 'rotate(0deg)';
                } else {
                    container.style.maxHeight = container.scrollHeight + "px";
                    if(arrowIcon) arrowIcon.style.transform = 'rotate(180deg)';
                }
            });
        });

        // --- File Drop Zone & Audio Player ---
        const dropZone = document.getElementById('drop-zone');
        const fileInput = document.getElementById('file-input');
        const dropZoneText = document.getElementById('drop-zone-text');
        
        const player = document.getElementById('audio-player');
        const audio = document.getElementById('audio-element');
        const playPauseBtn = document.getElementById('play-pause-btn');
        const playIcon = playPauseBtn.querySelector('span');
        const progressBar = document.getElementById('progress-bar');
        const progressBarContainer = document.getElementById('progress-bar-container');
        const timeDisplay = document.getElementById('time-display');
        const fileNameDisplay = document.getElementById('file-name');
        
        dropZone.addEventListener('dragover', (e) => { e.preventDefault(); dropZone.classList.add('drag-over'); });
        dropZone.addEventListener('dragleave', () => dropZone.classList.remove('drag-over'));
        dropZone.addEventListener('drop', (e) => {
            e.preventDefault();
            dropZone.classList.remove('drag-over');
            const files = e.dataTransfer.files;
            if (files.length > 0) handleFile(files[0]);
        });
        fileInput.addEventListener('change', () => {
            if (fileInput.files.length > 0) handleFile(fileInput.files[0]);
        });

        function handleFile(file) {
            if (file && file.type.startsWith('audio/')) {
                const fileURL = URL.createObjectURL(file);
                audio.src = fileURL;
                fileNameDisplay.textContent = file.name;
                player.style.display = 'block';
                dropZoneText.textContent = '已加载: ' + file.name;
            } else {
                dropZoneText.textContent = '文件类型无效，请选择音频文件';
                player.style.display = 'none';
            }
        }

        playPauseBtn.addEventListener('click', () => {
            if (audio.paused) audio.play(); else audio.pause();
        });
        audio.addEventListener('play', () => playIcon.textContent = 'pause');
        audio.addEventListener('pause', () => playIcon.textContent = 'play_arrow');
        
        function formatTime(seconds) {
            const min = Math.floor(seconds / 60);
            const sec = Math.floor(seconds % 60);
            return `${min}:${sec < 10 ? '0' : ''}${sec}`;
        }

        audio.addEventListener('timeupdate', () => {
            const { currentTime, duration } = audio;
            progressBar.style.width = `${(currentTime / duration) * 100}%`;
            if (!isNaN(duration)) {
                timeDisplay.textContent = `${formatTime(currentTime)} / ${formatTime(duration)}`;
            }
        });
        
        progressBarContainer.addEventListener('click', (e) => {
            const { clientWidth } = progressBarContainer;
            const clickX = e.offsetX;
            if (audio.duration) audio.currentTime = (clickX / clientWidth) * audio.duration;
        });
        
        // --- Context Menu for Song List ---
        const contextMenu = document.getElementById('song-context-menu');
        const songListContainer = document.getElementById('song-list');

        const showContextMenu = (event) => {
            event.preventDefault();
            contextMenu.style.display = 'block';
            const x = Math.min(event.clientX, window.innerWidth - contextMenu.offsetWidth - 10);
            const y = Math.min(event.clientY, window.innerHeight - contextMenu.offsetHeight - 10);
            contextMenu.style.left = `${x}px`;
            contextMenu.style.top = `${y}px`;
        };

        const hideContextMenu = () => {
            if (contextMenu.style.display === 'block') {
                contextMenu.style.display = 'none';
            }
        };

        const mockSongs = [
            '晴天 - 周杰伦.wav', '富士山下 - 陈奕迅.wav', '像我这样的人 - 毛不易.wav',
            '爱你 - 王心凌.wav', '稻香 - 周杰伦.wav', '起风了 - 吴青峰.wav', '孤勇者 - 陈奕迅.wav'
        ];

        function createSongItem(songName) {
            const item = document.createElement('div');
            item.className = 'song-item';
            item.innerHTML = `
                <span class="truncate">${songName}</span>
                <button class="song-item-menu-btn"><span class="material-icons">more_vert</span></button>
            `;
            item.addEventListener('contextmenu', showContextMenu);
            item.querySelector('.song-item-menu-btn').addEventListener('click', (event) => {
                event.stopPropagation();
                showContextMenu(event);
            });
            return item;
        }

        mockSongs.forEach(songName => {
            songListContainer.appendChild(createSongItem(songName));
        });

        document.addEventListener('click', hideContextMenu);
        document.addEventListener('keydown', (e) => {
            if (e.key === 'Escape') hideContextMenu();
        });
    </script>
</body>
</html>
