#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
样式管理器 - 负责加载和应用QSS样式表
"""

import os
from PySide6.QtWidgets import QApplication
from PySide6.QtCore import QFile, QTextStream


class StyleManager:
    """样式管理器类"""
    
    def __init__(self):
        self.current_theme = None
        self.styles_dir = os.path.dirname(os.path.abspath(__file__))
        
    def load_qss_file(self, qss_file_path):
        """加载QSS文件内容"""
        try:
            file = QFile(qss_file_path)
            if file.open(QFile.ReadOnly | QFile.Text):
                stream = QTextStream(file)
                stylesheet = stream.readAll()
                file.close()
                return stylesheet
            else:
                print(f"无法打开QSS文件: {qss_file_path}")
                return ""
        except Exception as e:
            print(f"加载QSS文件时出错: {e}")
            return ""
    
    def apply_dark_theme(self, app=None):
        """应用深色主题"""
        if app is None:
            app = QApplication.instance()
            
        if app is None:
            print("警告: 没有找到QApplication实例")
            return False
            
        qss_file = os.path.join(self.styles_dir, "dark_theme.qss")
        
        if not os.path.exists(qss_file):
            print(f"QSS文件不存在: {qss_file}")
            return False
            
        stylesheet = self.load_qss_file(qss_file)
        
        if stylesheet:
            app.setStyleSheet(stylesheet)
            self.current_theme = "dark"
            print("✓ 深色主题应用成功")
            return True
        else:
            print("✗ 深色主题应用失败")
            return False
    
    def get_current_theme(self):
        """获取当前主题"""
        return self.current_theme
    
    def reset_theme(self, app=None):
        """重置为默认主题"""
        if app is None:
            app = QApplication.instance()
            
        if app:
            app.setStyleSheet("")
            self.current_theme = None
            print("✓ 主题已重置为默认")


# 全局样式管理器实例
style_manager = StyleManager()
