/* AI翻唱应用程序 - 深色主题样式表 */
/* 基于HTML界面设计的颜色方案和视觉规格 */

/* 全局应用程序样式 */
QApplication {
    font-family: "Microsoft YaHei UI", "Segoe UI", "Roboto", sans-serif;
    font-size: 14px;
}

/* 主窗口样式 */
QMainWindow {
    background-color: #1E2A3A;
    color: #E5E7EB;
}

/* 通用Widget样式 */
QWidget {
    background-color: #1E2A3A;
    color: #E5E7EB;
    font-family: "Microsoft YaHei UI", "Segoe UI", "Roboto", sans-serif;
}

/* Frame/面板样式 - 类似HTML中的card */
QFrame {
    background-color: #293746;
    border: 1px solid #394B61;
    border-radius: 4px;
    padding: 16px;
}

/* 分割器样式 */
QSplitter::handle {
    background-color: #394B61;
    width: 2px;
    height: 2px;
}

QSplitter::handle:hover {
    background-color: #455A79;
}

/* 标签文本样式 */
QLabel {
    background-color: transparent;
    color: #E5E7EB;
    font-size: 14px;
    padding: 4px;
}

/* 标题样式 */
QLabel[class="title"] {
    font-size: 18px;
    font-weight: bold;
    color: #E5E7EB;
    padding: 8px;
}

/* 次要文本样式 */
QLabel[class="secondary"] {
    font-size: 12px;
    color: #B0BEC5;
    padding: 4px;
}

/* 按钮通用样式 */
QPushButton {
    background-color: #394B61;
    color: #E5E7EB;
    border: 1px solid #4B5563;
    border-radius: 4px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: 600;
    min-height: 20px;
}

QPushButton:hover {
    background-color: #455A79;
    border-color: #5B6B83;
}

QPushButton:pressed {
    background-color: #2A3441;
    border-color: #394B61;
}

QPushButton:disabled {
    background-color: #1E2A3A;
    color: #6B7280;
    border-color: #374151;
}

/* 绿色按钮样式 (成功/启动) */
QPushButton[class="success"] {
    background-color: #4CAF50;
    border-color: #4CAF50;
    transition: all 0.2s ease;
}

QPushButton[class="success"]:hover {
    background-color: #66BB6A;
    border-color: #66BB6A;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(76, 175, 80, 0.3);
}

QPushButton[class="success"]:pressed {
    background-color: #388E3C;
    border-color: #388E3C;
    transform: translateY(0px);
}

/* 红色按钮样式 (危险/停止) */
QPushButton[class="danger"] {
    background-color: #F44336;
    border-color: #F44336;
    transition: all 0.2s ease;
}

QPushButton[class="danger"]:hover {
    background-color: #EF5350;
    border-color: #EF5350;
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(244, 67, 54, 0.3);
}

QPushButton[class="danger"]:pressed {
    background-color: #D32F2F;
    border-color: #D32F2F;
    transform: translateY(0px);
}

/* 输入框样式 */
QLineEdit, QTextEdit, QPlainTextEdit {
    background-color: #394B61;
    color: #E5E7EB;
    border: 1px solid #4B5563;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    min-height: 20px;
}

QLineEdit:focus, QTextEdit:focus, QPlainTextEdit:focus {
    border-color: #1E88E5;
    outline: none;
}

/* 下拉框样式 */
QComboBox {
    background-color: #394B61;
    color: #E5E7EB;
    border: 1px solid #4B5563;
    border-radius: 4px;
    padding: 8px 12px;
    font-size: 14px;
    min-height: 20px;
}

QComboBox:hover {
    border-color: #5B6B83;
}

QComboBox:focus {
    border-color: #1E88E5;
}

QComboBox::drop-down {
    border: none;
    width: 20px;
}

QComboBox::down-arrow {
    image: url(data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTIiIGhlaWdodD0iOCIgdmlld0JveD0iMCAwIDEyIDgiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxwYXRoIGQ9Ik0xIDFMNiA2TDExIDEiIHN0cm9rZT0iI0U1RTdFQiIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+);
    width: 12px;
    height: 8px;
}

QComboBox QAbstractItemView {
    background-color: #293746;
    color: #E5E7EB;
    border: 1px solid #455A79;
    border-radius: 4px;
    selection-background-color: #1E88E5;
}

/* 滑块样式 */
QSlider::groove:horizontal {
    background-color: #394B61;
    height: 8px;
    border-radius: 4px;
}

QSlider::handle:horizontal {
    background-color: #1E88E5;
    width: 16px;
    height: 16px;
    border-radius: 8px;
    margin: -4px 0;
}

QSlider::handle:horizontal:hover {
    background-color: #42A5F5;
}

QSlider::handle:horizontal:pressed {
    background-color: #1976D2;
}

/* 复选框样式 */
QCheckBox {
    color: #E5E7EB;
    font-size: 14px;
    spacing: 8px;
}

QCheckBox::indicator {
    width: 16px;
    height: 16px;
    border: 1px solid #4B5563;
    border-radius: 2px;
    background-color: #394B61;
}

QCheckBox::indicator:hover {
    border-color: #5B6B83;
}

QCheckBox::indicator:checked {
    background-color: #1E88E5;
    border-color: #1E88E5;
}

/* 滚动条样式 */
QScrollBar:vertical {
    background-color: #293746;
    width: 8px;
    border-radius: 4px;
}

QScrollBar::handle:vertical {
    background-color: #455A79;
    border-radius: 4px;
    min-height: 20px;
    margin: 2px;
}

QScrollBar::handle:vertical:hover {
    background-color: #5B6B83;
}

QScrollBar::add-line:vertical, QScrollBar::sub-line:vertical {
    height: 0px;
}

QScrollBar:horizontal {
    background-color: #293746;
    height: 8px;
    border-radius: 4px;
}

QScrollBar::handle:horizontal {
    background-color: #455A79;
    border-radius: 4px;
    min-width: 20px;
    margin: 2px;
}

QScrollBar::handle:horizontal:hover {
    background-color: #5B6B83;
}

QScrollBar::add-line:horizontal, QScrollBar::sub-line:horizontal {
    width: 0px;
}

/* 标签页样式 */
QTabWidget::pane {
    border: 1px solid #394B61;
    background-color: #293746;
    border-radius: 4px;
}

QTabBar::tab {
    background-color: #293746;
    color: #B0BEC5;
    padding: 10px 20px;
    border: 1px solid transparent;
    border-bottom: none;
    border-top-left-radius: 4px;
    border-top-right-radius: 4px;
    margin-right: 2px;
}

QTabBar::tab:hover {
    background-color: #455A79;
}

QTabBar::tab:selected {
    background-color: #394B61;
    color: #E5E7EB;
    border-color: #394B61;
}

/* 工具提示样式 */
QToolTip {
    background-color: #293746;
    color: #E5E7EB;
    border: 1px solid #455A79;
    border-radius: 4px;
    padding: 8px;
    font-size: 12px;
}
